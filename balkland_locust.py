
from locust import HttpUser, task, between
import random

class BalklandSEOUser(HttpUser):
    wait_time = between(5, 15)  # Wait 5-15 seconds between requests

    def on_start(self):
        """Setup user session"""
        self.keywords = [
            "Balkland balkan tour",
            "Balkland balkan tour packages",
            "Balkland balkan tours",
            "Balkland balkan trip",
            "Balkland balkan tour from usa",
            "Balkland balkan vacation",
            "book Balkland tour",
            "Balkland tour booking"
        ]

        # Set realistic headers
        self.client.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive"
        })

    @task(10)
    def search_balkland(self):
        """Perform Balkland search with realistic behavior"""
        keyword = random.choice(self.keywords)
        search_params = {
            "q": keyword,
            "num": "20"
        }

        # Perform search
        with self.client.get("/search", params=search_params, catch_response=True) as response:
            if response.status_code == 200:
                if "balkland" in response.text.lower():
                    response.success()
                else:
                    response.failure("Balkland not found in results")
            else:
                response.failure(f"HTTP {response.status_code}")

    @task(3)
    def search_balkland_specific(self):
        """Specific Balkland searches"""
        specific_searches = [
            "Balkland tours Serbia",
            "Balkland tours Croatia",
            "Balkland tours Bosnia",
            "best Balkland tours",
            "Balkland tour reviews"
        ]

        keyword = random.choice(specific_searches)
        search_params = {
            "q": keyword,
            "num": "10"
        }

        with self.client.get("/search", params=search_params, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"HTTP {response.status_code}")

    @task(1)
    def visit_balkland_site(self):
        """Simulate visiting Balkland website"""
        # This would be used if we want to generate direct traffic
        pass
