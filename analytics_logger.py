"""
Advanced Analytics and Logging System

This module provides comprehensive logging, analytics, and monitoring
capabilities for the traffic generation system.
"""

import json
import csv
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from loguru import logger
import pandas as pd
from config_manager import config

@dataclass
class SessionMetrics:
    """Data class for session metrics"""
    session_id: str
    keyword: str
    target_url: str
    proxy_region: str
    user_agent: str
    start_time: datetime
    end_time: Optional[datetime] = None
    success: bool = False
    pages_visited: int = 0
    total_time_on_site: float = 0.0
    bounce: bool = True
    search_engine: str = "google.com"
    error_message: Optional[str] = None
    ip_address: Optional[str] = None
    device_type: str = "desktop"
    browser_type: str = "chrome"

@dataclass
class KeywordPerformance:
    """Data class for keyword performance metrics"""
    keyword: str
    total_searches: int = 0
    successful_visits: int = 0
    failed_visits: int = 0
    average_time_on_site: float = 0.0
    average_pages_per_visit: float = 0.0
    bounce_rate: float = 0.0
    last_updated: datetime = None

class AnalyticsLogger:
    """Advanced analytics and logging system"""
    
    def __init__(self, log_path: str = None):
        """Initialize analytics logger"""
        self.log_path = Path(log_path or config.analytics.log_file)
        self.log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database
        self.db_path = self.log_path.parent / "analytics.db"
        self._init_database()
        
        # Performance tracking
        self.session_cache: Dict[str, SessionMetrics] = {}
        self.keyword_performance: Dict[str, KeywordPerformance] = {}
        
        # Load existing keyword performance
        self._load_keyword_performance()
        
        logger.info(f"Analytics logger initialized: {self.log_path}")
    
    def _init_database(self):
        """Initialize SQLite database for analytics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Sessions table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS sessions (
                        session_id TEXT PRIMARY KEY,
                        keyword TEXT NOT NULL,
                        target_url TEXT NOT NULL,
                        proxy_region TEXT,
                        user_agent TEXT,
                        start_time TIMESTAMP NOT NULL,
                        end_time TIMESTAMP,
                        success BOOLEAN NOT NULL,
                        pages_visited INTEGER DEFAULT 0,
                        total_time_on_site REAL DEFAULT 0.0,
                        bounce BOOLEAN DEFAULT TRUE,
                        search_engine TEXT DEFAULT 'google.com',
                        error_message TEXT,
                        ip_address TEXT,
                        device_type TEXT DEFAULT 'desktop',
                        browser_type TEXT DEFAULT 'chrome'
                    )
                """)
                
                # Keyword performance table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS keyword_performance (
                        keyword TEXT PRIMARY KEY,
                        total_searches INTEGER DEFAULT 0,
                        successful_visits INTEGER DEFAULT 0,
                        failed_visits INTEGER DEFAULT 0,
                        average_time_on_site REAL DEFAULT 0.0,
                        average_pages_per_visit REAL DEFAULT 0.0,
                        bounce_rate REAL DEFAULT 0.0,
                        last_updated TIMESTAMP
                    )
                """)
                
                # Daily summary table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS daily_summary (
                        date DATE PRIMARY KEY,
                        total_sessions INTEGER DEFAULT 0,
                        successful_sessions INTEGER DEFAULT 0,
                        failed_sessions INTEGER DEFAULT 0,
                        total_time_on_site REAL DEFAULT 0.0,
                        average_session_duration REAL DEFAULT 0.0,
                        unique_keywords INTEGER DEFAULT 0,
                        bounce_rate REAL DEFAULT 0.0
                    )
                """)
                
                # Error log table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS error_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TIMESTAMP NOT NULL,
                        session_id TEXT,
                        keyword TEXT,
                        error_type TEXT NOT NULL,
                        error_message TEXT NOT NULL,
                        stack_trace TEXT
                    )
                """)
                
                conn.commit()
                logger.debug("Database initialized successfully")
                
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def start_session(self, session_id: str, keyword: str, target_url: str,
                     proxy_region: str, user_agent: str, device_type: str = "desktop",
                     browser_type: str = "chrome") -> SessionMetrics:
        """Start tracking a new session"""
        session = SessionMetrics(
            session_id=session_id,
            keyword=keyword,
            target_url=target_url,
            proxy_region=proxy_region,
            user_agent=user_agent,
            start_time=datetime.now(),
            device_type=device_type,
            browser_type=browser_type
        )
        
        self.session_cache[session_id] = session
        logger.debug(f"Started session tracking: {session_id}")
        return session
    
    def end_session(self, session_id: str, success: bool, pages_visited: int = 0,
                   error_message: str = None, ip_address: str = None):
        """End session tracking and save to database"""
        if session_id not in self.session_cache:
            logger.warning(f"Session not found in cache: {session_id}")
            return
        
        session = self.session_cache[session_id]
        session.end_time = datetime.now()
        session.success = success
        session.pages_visited = pages_visited
        session.error_message = error_message
        session.ip_address = ip_address
        
        # Calculate metrics
        if session.end_time and session.start_time:
            session.total_time_on_site = (session.end_time - session.start_time).total_seconds()
        
        session.bounce = pages_visited <= 1
        
        # Save to database
        self._save_session_to_db(session)
        
        # Update keyword performance
        self._update_keyword_performance(session)
        
        # Remove from cache
        del self.session_cache[session_id]
        
        logger.debug(f"Ended session tracking: {session_id} (Success: {success})")
    
    def _save_session_to_db(self, session: SessionMetrics):
        """Save session data to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO sessions VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    session.session_id, session.keyword, session.target_url,
                    session.proxy_region, session.user_agent, session.start_time,
                    session.end_time, session.success, session.pages_visited,
                    session.total_time_on_site, session.bounce, session.search_engine,
                    session.error_message, session.ip_address, session.device_type,
                    session.browser_type
                ))
                conn.commit()
                
        except Exception as e:
            logger.error(f"Failed to save session to database: {e}")
    
    def _update_keyword_performance(self, session: SessionMetrics):
        """Update keyword performance metrics"""
        keyword = session.keyword
        
        if keyword not in self.keyword_performance:
            self.keyword_performance[keyword] = KeywordPerformance(keyword=keyword)
        
        perf = self.keyword_performance[keyword]
        perf.total_searches += 1
        perf.last_updated = datetime.now()
        
        if session.success:
            perf.successful_visits += 1
            
            # Update averages (running average)
            total_successful = perf.successful_visits
            perf.average_time_on_site = (
                (perf.average_time_on_site * (total_successful - 1) + session.total_time_on_site) 
                / total_successful
            )
            perf.average_pages_per_visit = (
                (perf.average_pages_per_visit * (total_successful - 1) + session.pages_visited)
                / total_successful
            )
        else:
            perf.failed_visits += 1
        
        # Calculate bounce rate
        if perf.successful_visits > 0:
            # Get bounce data from database for this keyword
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        SELECT COUNT(*) as bounces FROM sessions 
                        WHERE keyword = ? AND success = TRUE AND bounce = TRUE
                    """, (keyword,))
                    bounces = cursor.fetchone()[0]
                    perf.bounce_rate = bounces / perf.successful_visits
            except:
                pass
        
        # Save to database
        self._save_keyword_performance_to_db(perf)
    
    def _save_keyword_performance_to_db(self, perf: KeywordPerformance):
        """Save keyword performance to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO keyword_performance VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    perf.keyword, perf.total_searches, perf.successful_visits,
                    perf.failed_visits, perf.average_time_on_site,
                    perf.average_pages_per_visit, perf.bounce_rate, perf.last_updated
                ))
                conn.commit()
                
        except Exception as e:
            logger.error(f"Failed to save keyword performance: {e}")
    
    def _load_keyword_performance(self):
        """Load existing keyword performance from database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM keyword_performance")
                rows = cursor.fetchall()
                
                for row in rows:
                    keyword = row[0]
                    self.keyword_performance[keyword] = KeywordPerformance(
                        keyword=keyword,
                        total_searches=row[1],
                        successful_visits=row[2],
                        failed_visits=row[3],
                        average_time_on_site=row[4],
                        average_pages_per_visit=row[5],
                        bounce_rate=row[6],
                        last_updated=datetime.fromisoformat(row[7]) if row[7] else None
                    )
                
                logger.debug(f"Loaded {len(self.keyword_performance)} keyword performance records")
                
        except Exception as e:
            logger.debug(f"No existing keyword performance data: {e}")
    
    def log_error(self, session_id: str, keyword: str, error_type: str,
                 error_message: str, stack_trace: str = None):
        """Log error to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO error_log (timestamp, session_id, keyword, error_type, error_message, stack_trace)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (datetime.now(), session_id, keyword, error_type, error_message, stack_trace))
                conn.commit()
                
            logger.error(f"Error logged: {error_type} - {error_message}")
            
        except Exception as e:
            logger.error(f"Failed to log error to database: {e}")
    
    def generate_daily_summary(self, date: datetime = None) -> Dict[str, Any]:
        """Generate daily summary report"""
        if date is None:
            date = datetime.now().date()
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get daily statistics
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_sessions,
                        SUM(CASE WHEN success = TRUE THEN 1 ELSE 0 END) as successful_sessions,
                        SUM(CASE WHEN success = FALSE THEN 1 ELSE 0 END) as failed_sessions,
                        SUM(total_time_on_site) as total_time_on_site,
                        AVG(total_time_on_site) as avg_session_duration,
                        COUNT(DISTINCT keyword) as unique_keywords,
                        AVG(CASE WHEN success = TRUE THEN CAST(bounce AS REAL) ELSE NULL END) as bounce_rate
                    FROM sessions 
                    WHERE DATE(start_time) = ?
                """, (date,))
                
                row = cursor.fetchone()
                
                summary = {
                    'date': date.isoformat(),
                    'total_sessions': row[0] or 0,
                    'successful_sessions': row[1] or 0,
                    'failed_sessions': row[2] or 0,
                    'success_rate': (row[1] or 0) / max(row[0] or 1, 1),
                    'total_time_on_site': row[3] or 0.0,
                    'average_session_duration': row[4] or 0.0,
                    'unique_keywords': row[5] or 0,
                    'bounce_rate': row[6] or 0.0
                }
                
                # Save to daily summary table
                cursor.execute("""
                    INSERT OR REPLACE INTO daily_summary VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    date, summary['total_sessions'], summary['successful_sessions'],
                    summary['failed_sessions'], summary['total_time_on_site'],
                    summary['average_session_duration'], summary['unique_keywords'],
                    summary['bounce_rate']
                ))
                conn.commit()
                
                return summary
                
        except Exception as e:
            logger.error(f"Failed to generate daily summary: {e}")
            return {}
    
    def get_keyword_performance_report(self) -> List[Dict[str, Any]]:
        """Get keyword performance report"""
        return [asdict(perf) for perf in self.keyword_performance.values()]
    
    def export_data(self, format_type: str = "json", date_range: int = 7) -> str:
        """Export analytics data in specified format"""
        try:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=date_range)
            
            with sqlite3.connect(self.db_path) as conn:
                # Export sessions data
                sessions_df = pd.read_sql_query("""
                    SELECT * FROM sessions 
                    WHERE DATE(start_time) BETWEEN ? AND ?
                    ORDER BY start_time DESC
                """, conn, params=(start_date, end_date))
                
                # Export keyword performance
                keywords_df = pd.read_sql_query("""
                    SELECT * FROM keyword_performance 
                    ORDER BY total_searches DESC
                """, conn)
                
                # Export daily summaries
                daily_df = pd.read_sql_query("""
                    SELECT * FROM daily_summary 
                    WHERE date BETWEEN ? AND ?
                    ORDER BY date DESC
                """, conn, params=(start_date, end_date))
            
            # Create export directory
            export_dir = Path(config.get_environment_config()['report_export_path'])
            export_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if format_type.lower() == "json":
                export_file = export_dir / f"analytics_export_{timestamp}.json"
                data = {
                    'sessions': sessions_df.to_dict('records'),
                    'keyword_performance': keywords_df.to_dict('records'),
                    'daily_summary': daily_df.to_dict('records'),
                    'export_info': {
                        'timestamp': datetime.now().isoformat(),
                        'date_range': f"{start_date} to {end_date}",
                        'total_sessions': len(sessions_df)
                    }
                }
                
                with open(export_file, 'w') as f:
                    json.dump(data, f, indent=2, default=str)
            
            elif format_type.lower() == "csv":
                export_file = export_dir / f"analytics_export_{timestamp}.zip"
                # Save multiple CSV files in a zip
                sessions_df.to_csv(export_dir / f"sessions_{timestamp}.csv", index=False)
                keywords_df.to_csv(export_dir / f"keywords_{timestamp}.csv", index=False)
                daily_df.to_csv(export_dir / f"daily_summary_{timestamp}.csv", index=False)
                
                # Create zip file
                import zipfile
                with zipfile.ZipFile(export_file, 'w') as zipf:
                    zipf.write(export_dir / f"sessions_{timestamp}.csv", f"sessions_{timestamp}.csv")
                    zipf.write(export_dir / f"keywords_{timestamp}.csv", f"keywords_{timestamp}.csv")
                    zipf.write(export_dir / f"daily_summary_{timestamp}.csv", f"daily_summary_{timestamp}.csv")
                
                # Clean up individual CSV files
                (export_dir / f"sessions_{timestamp}.csv").unlink()
                (export_dir / f"keywords_{timestamp}.csv").unlink()
                (export_dir / f"daily_summary_{timestamp}.csv").unlink()
            
            logger.info(f"Data exported to: {export_file}")
            return str(export_file)
            
        except Exception as e:
            logger.error(f"Failed to export data: {e}")
            return ""
