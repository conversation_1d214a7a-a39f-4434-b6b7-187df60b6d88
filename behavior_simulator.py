"""
Human-Like Behavior Simulation Engine

This module simulates realistic human browsing behavior including mouse movements,
scrolling patterns, reading time, and natural interaction flows.
"""

import asyncio
import random
import math
from typing import Tu<PERSON>, List, Optional
from playwright.async_api import Page
from loguru import logger
from config_manager import config

class BehaviorSimulator:
    """Simulates human-like browsing behavior"""
    
    def __init__(self):
        """Initialize behavior simulator with configuration"""
        self.behavior_config = config.behavior
        self.reading_config = self.behavior_config.reading
        self.scrolling_config = self.behavior_config.scrolling
        self.mouse_config = self.behavior_config.mouse
    
    async def simulate_human_delay(self, min_delay: float = 0.5, max_delay: float = 2.0):
        """Simulate human-like delay with natural variation"""
        # Use gamma distribution for more realistic timing
        delay = random.gammavariate(2, (min_delay + max_delay) / 4)
        delay = max(min_delay, min(max_delay, delay))
        await asyncio.sleep(delay)
    
    async def simulate_typing(self, page: Page, selector: str, text: str, 
                            human_like: bool = True):
        """Simulate human-like typing with realistic delays"""
        try:
            element = await page.wait_for_selector(selector, timeout=10000)
            
            if not human_like:
                await element.fill(text)
                return
            
            # Clear existing text
            await element.click()
            await page.keyboard.press('Control+a')
            
            # Type character by character with human-like delays
            for char in text:
                await element.type(char)
                
                # Variable typing speed based on character type
                if char == ' ':
                    delay = random.uniform(0.1, 0.3)  # Longer pause for spaces
                elif char in '.,!?;:':
                    delay = random.uniform(0.2, 0.4)  # Pause for punctuation
                elif char.isupper():
                    delay = random.uniform(0.1, 0.25)  # Slight pause for capitals
                else:
                    delay = random.uniform(0.05, 0.15)  # Normal typing speed
                
                # Add occasional longer pauses (thinking time)
                if random.random() < 0.05:  # 5% chance
                    delay += random.uniform(0.5, 1.5)
                
                await asyncio.sleep(delay)
            
            logger.debug(f"Typed text: {text[:20]}...")
            
        except Exception as e:
            logger.error(f"Error simulating typing: {e}")
            # Fallback to simple fill
            await page.fill(selector, text)
    
    async def simulate_mouse_movement(self, page: Page, target_x: int, target_y: int,
                                    duration: float = 1.0):
        """Simulate realistic mouse movement with bezier curves"""
        if not self.mouse_config.get('enable_movements', True):
            await page.mouse.move(target_x, target_y)
            return
        
        try:
            # Get current mouse position (start from random position if unknown)
            current_x = random.randint(100, 800)
            current_y = random.randint(100, 600)
            
            # Generate bezier curve points for natural movement
            steps = max(10, int(duration * 30))  # 30 steps per second
            points = self._generate_bezier_curve(
                current_x, current_y, target_x, target_y, steps
            )
            
            # Move mouse along the curve
            for i, (x, y) in enumerate(points):
                await page.mouse.move(x, y)
                
                # Variable speed - slower at start and end
                progress = i / len(points)
                speed_factor = 4 * progress * (1 - progress) + 0.1
                delay = (duration / steps) / speed_factor
                
                await asyncio.sleep(delay)
            
            # Final move to exact target
            await page.mouse.move(target_x, target_y)
            
        except Exception as e:
            logger.error(f"Error simulating mouse movement: {e}")
            await page.mouse.move(target_x, target_y)
    
    def _generate_bezier_curve(self, x1: int, y1: int, x2: int, y2: int, 
                              steps: int) -> List[Tuple[int, int]]:
        """Generate bezier curve points for natural mouse movement"""
        # Add control points for curve
        control1_x = x1 + random.randint(-100, 100)
        control1_y = y1 + random.randint(-100, 100)
        control2_x = x2 + random.randint(-100, 100)
        control2_y = y2 + random.randint(-100, 100)
        
        points = []
        for i in range(steps + 1):
            t = i / steps
            
            # Cubic bezier formula
            x = (1-t)**3 * x1 + 3*(1-t)**2*t * control1_x + 3*(1-t)*t**2 * control2_x + t**3 * x2
            y = (1-t)**3 * y1 + 3*(1-t)**2*t * control1_y + 3*(1-t)*t**2 * control2_y + t**3 * y2
            
            points.append((int(x), int(y)))
        
        return points
    
    async def simulate_scrolling(self, page: Page, target_depth: float = None):
        """Simulate human-like scrolling behavior"""
        try:
            # Get page dimensions
            page_height = await page.evaluate('document.body.scrollHeight')
            viewport_height = await page.evaluate('window.innerHeight')
            
            if page_height <= viewport_height:
                return  # No need to scroll
            
            # Determine scroll depth
            if target_depth is None:
                min_depth, max_depth = self.scrolling_config['depth']
                target_depth = random.uniform(min_depth, max_depth)
            
            target_scroll = int(page_height * target_depth)
            current_scroll = 0
            
            # Scroll in natural chunks
            while current_scroll < target_scroll:
                # Variable scroll distance
                scroll_distance = random.randint(100, 400)
                scroll_distance = min(scroll_distance, target_scroll - current_scroll)
                
                # Scroll with mouse wheel simulation
                await page.mouse.wheel(0, scroll_distance)
                current_scroll += scroll_distance
                
                # Natural pause between scrolls
                pause_min, pause_max = self.scrolling_config['pauses']
                pause = random.uniform(pause_min, pause_max)
                await asyncio.sleep(pause)
                
                # Occasionally scroll back up slightly (reading behavior)
                if random.random() < 0.1:  # 10% chance
                    back_scroll = random.randint(50, 150)
                    await page.mouse.wheel(0, -back_scroll)
                    await asyncio.sleep(random.uniform(0.5, 1.0))
                    await page.mouse.wheel(0, back_scroll)
            
            logger.debug(f"Scrolled to {target_depth:.1%} of page")
            
        except Exception as e:
            logger.error(f"Error simulating scrolling: {e}")
    
    async def simulate_reading_behavior(self, page: Page, duration: int = None):
        """Simulate realistic reading behavior on a page (180-240 seconds for high engagement)"""
        try:
            if duration is None:
                # High engagement: 180-240 seconds (3-4 minutes)
                duration = random.randint(180, 240)
            
            start_time = asyncio.get_event_loop().time()
            end_time = start_time + duration
            
            # Break reading time into natural segments
            segments = random.randint(3, 8)
            segment_duration = duration / segments
            
            for segment in range(segments):
                # Scroll to different parts of the page
                scroll_position = random.uniform(0.1, 0.9)
                await self.simulate_scrolling(page, scroll_position)
                
                # Simulate reading time for this segment
                read_time = segment_duration * random.uniform(0.7, 1.3)
                
                # Add micro-interactions during reading
                await self._simulate_reading_micro_interactions(page, read_time)
                
                # Check if we've reached the target duration
                current_time = asyncio.get_event_loop().time()
                if current_time >= end_time:
                    break
            
            logger.debug(f"Simulated reading for {duration} seconds")
            
        except Exception as e:
            logger.error(f"Error simulating reading behavior: {e}")
    
    async def _simulate_reading_micro_interactions(self, page: Page, duration: float):
        """Simulate micro-interactions during reading"""
        end_time = asyncio.get_event_loop().time() + duration
        
        while asyncio.get_event_loop().time() < end_time:
            action_type = random.choices(
                ['pause', 'small_scroll', 'text_selection', 'mouse_move'],
                weights=[0.4, 0.3, 0.1, 0.2]
            )[0]
            
            if action_type == 'pause':
                # Just pause (reading)
                await asyncio.sleep(random.uniform(2, 8))
            
            elif action_type == 'small_scroll':
                # Small scroll adjustment
                scroll_amount = random.randint(50, 200)
                direction = random.choice([1, -1])
                await page.mouse.wheel(0, scroll_amount * direction)
                await asyncio.sleep(random.uniform(0.5, 2))
            
            elif action_type == 'text_selection':
                # Simulate text selection (curiosity behavior)
                try:
                    # Find text elements
                    text_elements = await page.query_selector_all('p, span, div, h1, h2, h3')
                    if text_elements:
                        element = random.choice(text_elements)
                        box = await element.bounding_box()
                        if box:
                            # Click and drag to select text
                            start_x = box['x'] + random.randint(0, int(box['width'] * 0.8))
                            start_y = box['y'] + box['height'] // 2
                            end_x = start_x + random.randint(50, 200)
                            
                            await page.mouse.move(start_x, start_y)
                            await page.mouse.down()
                            await self.simulate_mouse_movement(page, end_x, start_y, 0.5)
                            await page.mouse.up()
                            await asyncio.sleep(random.uniform(1, 3))
                            
                            # Click elsewhere to deselect
                            await page.mouse.click(start_x + 100, start_y + 50)
                except:
                    pass  # Ignore errors in text selection
            
            elif action_type == 'mouse_move':
                # Random mouse movement (natural fidgeting)
                viewport = await page.viewport_size()
                target_x = random.randint(100, viewport['width'] - 100)
                target_y = random.randint(100, viewport['height'] - 100)
                await self.simulate_mouse_movement(page, target_x, target_y, 1.0)
                await asyncio.sleep(random.uniform(0.5, 2))
    
    async def simulate_link_clicking(self, page: Page, selector: str, 
                                   new_tab: bool = False):
        """Simulate human-like link clicking"""
        try:
            element = await page.wait_for_selector(selector, timeout=10000)
            box = await element.bounding_box()
            
            if not box:
                await element.click()
                return
            
            # Move mouse to element with natural movement
            target_x = box['x'] + box['width'] // 2 + random.randint(-10, 10)
            target_y = box['y'] + box['height'] // 2 + random.randint(-5, 5)
            
            await self.simulate_mouse_movement(page, target_x, target_y, 
                                             random.uniform(0.5, 1.5))
            
            # Hover briefly before clicking
            await asyncio.sleep(random.uniform(0.2, 0.8))
            
            # Click with optional modifier for new tab
            if new_tab:
                await page.keyboard.down('Control')
                await page.mouse.click(target_x, target_y)
                await page.keyboard.up('Control')
            else:
                await page.mouse.click(target_x, target_y)
            
            logger.debug(f"Clicked element: {selector}")
            
        except Exception as e:
            logger.error(f"Error simulating link click: {e}")
            # Fallback to simple click
            await page.click(selector)
    
    async def simulate_form_interaction(self, page: Page, form_data: dict):
        """Simulate human-like form filling"""
        try:
            for field_selector, value in form_data.items():
                # Wait for field to be available
                await page.wait_for_selector(field_selector, timeout=5000)
                
                # Click on field
                await self.simulate_link_clicking(page, field_selector)
                await self.simulate_human_delay(0.3, 1.0)
                
                # Type value
                await self.simulate_typing(page, field_selector, str(value))
                await self.simulate_human_delay(0.5, 1.5)
            
            logger.debug("Form interaction completed")
            
        except Exception as e:
            logger.error(f"Error simulating form interaction: {e}")
    
    async def simulate_page_exploration(self, page: Page, exploration_time: int = 30):
        """Simulate exploratory behavior on a page"""
        try:
            end_time = asyncio.get_event_loop().time() + exploration_time
            
            while asyncio.get_event_loop().time() < end_time:
                action = random.choices(
                    ['scroll', 'hover_link', 'read_pause', 'mouse_move'],
                    weights=[0.3, 0.2, 0.3, 0.2]
                )[0]
                
                if action == 'scroll':
                    await self.simulate_scrolling(page, random.uniform(0.2, 0.8))
                
                elif action == 'hover_link':
                    try:
                        links = await page.query_selector_all('a')
                        if links:
                            link = random.choice(links)
                            box = await link.bounding_box()
                            if box:
                                await self.simulate_mouse_movement(
                                    page, 
                                    box['x'] + box['width'] // 2,
                                    box['y'] + box['height'] // 2,
                                    random.uniform(0.5, 1.0)
                                )
                                await asyncio.sleep(random.uniform(1, 3))
                    except:
                        pass
                
                elif action == 'read_pause':
                    await asyncio.sleep(random.uniform(3, 10))
                
                elif action == 'mouse_move':
                    viewport = await page.viewport_size()
                    target_x = random.randint(100, viewport['width'] - 100)
                    target_y = random.randint(100, viewport['height'] - 100)
                    await self.simulate_mouse_movement(page, target_x, target_y, 1.0)
                
                await self.simulate_human_delay(1, 3)
            
            logger.debug(f"Page exploration completed ({exploration_time}s)")
            
        except Exception as e:
            logger.error(f"Error during page exploration: {e}")
