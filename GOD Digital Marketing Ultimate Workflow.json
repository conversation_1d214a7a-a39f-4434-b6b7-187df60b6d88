{
  "name": "GOD Digital Marketing Ultimate Workflow",
  "nodes": [
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "field": "cronExpression",
              "expression": "0 8,13,18 * * 1,2,3,4,5,6,7"
            }
          ]
        }
      },
      "id": "smart-schedule-001",
      "name": "Smart Daily Scheduler",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.1,
      "position": [-2400, 0]
    },
    {
      "parameters": {},
      "id": "manual-trigger-001",
      "name": "Manual Test Trigger",
      "type": "n8n-nodes-base.manualTrigger",
      "typeVersion": 1,
      "position": [-2400, 100]
    },
    {
      "parameters": {
        "jsCode": "// GOD Digital Marketing - Ultimate Strategic Configuration\nconst currentDay = new Date().getDay(); // 0 = Sunday, 1 = Monday, etc.\nconst rotationDay = currentDay === 0 ? 7 : currentDay; // Convert Sunday to 7 for 7-day rotation\n\nconst godDigitalConfig = {\n  // Company Branding\n  business_type: 'GOD Digital Marketing - Full-Service Digital Transformation Agency',\n  company_name: 'GOD Digital Marketing',\n  website: 'https://godigitalmarketing.com',\n  target_audience: 'Business Owners, Entrepreneurs, Marketing Directors, Startups, E-commerce Brands, SaaS Companies',\n  value_proposition: 'We Transform Businesses Through AI-Powered Digital Marketing, Automation & Development Solutions That Generate 500%+ ROI',\n  \n  // Complete Service Portfolio\n  services_offered: {\n    core_digital_marketing: [\n      'Search Engine Optimization (SEO)',\n      'Pay-Per-Click Advertising (PPC/SEM)',\n      'Social Media Marketing & Management',\n      'Content Marketing & Strategy',\n      'Email Marketing & Automation',\n      'Influencer Marketing',\n      'Conversion Rate Optimization (CRO)',\n      'Marketing Analytics & Reporting',\n      'Brand Strategy & Positioning',\n      'Online Reputation Management'\n    ],\n    technology_automation: [\n      'AI Automation Solutions (ChatGPT integrations, AI chatbots)',\n      'Business Process Automation (Workflow optimization, CRM automation)',\n      'Web Development (Custom websites, landing pages, e-commerce)',\n      'App Development (Mobile applications, progressive web apps)',\n      'Marketing Automation Systems',\n      'Lead Nurturing & Scoring Systems',\n      'Customer Journey Automation',\n      'n8n Workflow Development'\n    ]\n  },\n  \n  // Strategic 7-Day Content Rotation Calendar\n  content_strategy: {\n    rotation_cycle: 7,\n    current_day: rotationDay,\n    strategies: {\n      1: { // Monday - Educational Excellence\n        type: 'educational',\n        focus: 'Educational Excellence - Establish Authority',\n        goal: 'Establish authority and provide genuine value',\n        psychological_trigger: 'Authority & Trust Building',\n        content_pillars: ['In-depth tutorials', 'Case studies', 'How-to guides', 'Industry insights'],\n        cta_examples: ['Save this post for later', 'Share with someone who needs this'],\n        formats: ['Carousel posts', 'Video tutorials', 'Infographic breakdowns']\n      },\n      2: { // Tuesday - Achievement Showcase\n        type: 'achievements',\n        focus: 'Achievement Showcase - Build Credibility',\n        goal: 'Build credibility through social proof',\n        psychological_trigger: 'FOMO & Aspiration',\n        content_pillars: ['Client results', 'Project highlights', 'Team accomplishments', 'Success metrics'],\n        cta_examples: ['Ready for similar results?', 'DM us to discuss your project'],\n        formats: ['Before/after results', 'Testimonial videos', 'Case study snippets']\n      },\n      3: { // Wednesday - Strategic Marketing Nudge\n        type: 'marketing_psychology',\n        focus: 'Strategic Marketing Nudge - Create Urgency',\n        goal: 'Create urgency and highlight pain points',\n        psychological_trigger: 'Loss Aversion & Urgency',\n        content_pillars: ['What you\\'re missing', 'Market insights', 'Competitor analysis', 'Problem agitation'],\n        cta_examples: ['Don\\'t let competitors get ahead', 'Book a free strategy call'],\n        formats: ['Problem-agitation-solution posts', 'Myth-busting content']\n      },\n      4: { // Thursday - Industry Trends & News\n        type: 'trends_news',\n        focus: 'Industry Trends & News - Thought Leadership',\n        goal: 'Position as industry thought leader',\n        psychological_trigger: 'Authority & Insider Knowledge',\n        content_pillars: ['Algorithm updates', 'Emerging technologies', 'Market predictions', 'Industry analysis'],\n        cta_examples: ['What\\'s your take on this?', 'Stay ahead with our insights'],\n        formats: ['News commentary', 'Trend analysis', 'Prediction posts']\n      },\n      5: { // Friday - Value-Driven Freebies\n        type: 'free_resources',\n        focus: 'Value-Driven Freebies - Lead Generation',\n        goal: 'Build email list and demonstrate expertise',\n        psychological_trigger: 'Reciprocity Principle',\n        content_pillars: ['Free resources', 'Templates', 'Checklists', 'Mini-courses', 'n8n workflows'],\n        cta_examples: ['Download for free in our bio', 'Get instant access'],\n        formats: ['PDF downloads', 'N8N workflows', 'Automation templates']\n      },\n      6: { // Saturday - Community Engagement\n        type: 'community',\n        focus: 'Community Engagement - Foster Relationships',\n        goal: 'Foster relationships and gather insights',\n        psychological_trigger: 'Belonging & Community',\n        content_pillars: ['Polls', 'Q&A sessions', 'Behind-the-scenes', 'Team spotlights'],\n        cta_examples: ['Tell us in the comments', 'Join the conversation'],\n        formats: ['Interactive posts', 'Live sessions', 'Team spotlights']\n      },\n      7: { // Sunday - Motivational & Inspirational\n        type: 'motivational',\n        focus: 'Motivational & Inspirational - Emotional Connection',\n        goal: 'Inspire action and emotional connection',\n        psychological_trigger: 'Inspiration & Emotional Triggers',\n        content_pillars: ['Success stories', 'Motivational content', 'Journey stories', 'Transformation narratives'],\n        cta_examples: ['Tag someone who needs to see this', 'Your journey starts today'],\n        formats: ['Quote graphics', 'Story posts', 'Transformation narratives']\n      }\n    }\n  },\n  \n  // Brand Voice & Personality\n  brand_voice: 'Professional yet approachable, results-driven, innovative, trustworthy, and community-focused. We speak with confidence backed by proven results and cutting-edge expertise.',\n  brand_personality: ['Expert Authority', 'Innovation Leader', 'Results-Driven', 'Community Builder', 'Problem Solver'],\n  \n  // Platform-Specific Strategies\n  platform_strategies: {\n    linkedin: {\n      focus: 'Professional B2B content',\n      tone: 'Professional with data-driven insights',\n      content_emphasis: 'ROI and business growth metrics'\n    },\n    instagram: {\n      focus: 'Visual storytelling',\n      tone: 'Creative and engaging',\n      content_emphasis: 'High-quality graphics, Stories, Reels'\n    },\n    facebook: {\n      focus: 'Community building',\n      tone: 'Conversational and educational',\n      content_emphasis: 'Longer-form content, live sessions'\n    },\n    twitter: {\n      focus: 'Real-time engagement',\n      tone: 'Quick and insightful',\n      content_emphasis: 'Industry news, thread breakdowns'\n    },\n    tiktok: {\n      focus: 'Trending content',\n      tone: 'Fun and educational',\n      content_emphasis: 'Quick tips, behind-the-scenes'\n    }\n  },\n  \n  // Strategic Hashtag Sets\n  hashtag_sets: {\n    educational: '#DigitalMarketing #MarketingTips #BusinessGrowth #MarketingStrategy #DigitalTransformation #MarketingEducation #GODDigitalMarketing',\n    achievements: '#ClientSuccess #CaseStudy #MarketingResults #BusinessSuccess #ROI #MarketingWins #GrowthResults #GODDigitalMarketing',\n    marketing_psychology: '#MarketingPsychology #ConsumerBehavior #MarketingStrategy #BusinessPsychology #ConversionOptimization #GODDigitalMarketing',\n    trends_news: '#MarketingTrends #DigitalTrends #FutureOfMarketing #MarketingInnovation #TechTrends #IndustryNews #GODDigitalMarketing',\n    free_resources: '#FreeResources #MarketingTools #Templates #Guides #MarketingFreebies #ValueFirst #CommunityFirst #GODDigitalMarketing',\n    community: '#CommunityFirst #MarketingCommunity #NetworkingTips #BusinessNetworking #MarketingSupport #GODDigitalMarketing',\n    motivational: '#MondayMotivation #BusinessInspiration #EntrepreneurLife #SuccessStories #MarketingMotivation #GODDigitalMarketing',\n    general: '#GODDigitalMarketing #DigitalMarketing #BusinessGrowth #MarketingAutomation #LeadGeneration #DigitalTransformation',\n    ai: '#AIMarketing #MarketingAI #AutomationTools #TechMarketing #DigitalTransformation #AIAutomation #GODDigitalMarketing',\n    business: '#SmallBusiness #Entrepreneur #BusinessTips #GrowthHacking #BusinessStrategy #StartupGrowth #GODDigitalMarketing'\n  },\n  \n  // Quality & Performance Thresholds\n  quality_thresholds: {\n    min_content_length: 100,\n    max_content_length: 2500,\n    min_engagement_score: 8,\n    required_platforms: 8,\n    min_cta_strength: 7\n  },\n  \n  // Monthly Content Themes\n  monthly_themes: {\n    1: 'New Year, New Marketing Strategy',\n    2: 'Love Your Metrics - Analytics Deep Dive',\n    3: 'Spring Into Action - Q1 Review & Strategy',\n    4: 'Algorithm Updates & Adaptations',\n    5: 'Maximum Growth Strategies',\n    6: 'Mid-Year Marketing Mastery',\n    7: 'Summer of Automation',\n    8: 'AI Revolution in Marketing',\n    9: 'Back-to-Business Strategies',\n    10: 'Optimization October',\n    11: 'Thanksgiving for Growth',\n    12: 'Year-End Results & 2025 Planning'\n  },\n  \n  // Current Strategy for Today\n  todays_strategy: null // Will be set dynamically\n};\n\n// Set today's strategy\ngodDigitalConfig.todays_strategy = godDigitalConfig.content_strategy.strategies[rotationDay];\n\nreturn {\n  ...godDigitalConfig,\n  config_loaded: true,\n  config_source: 'god_digital_ultimate_7day',\n  rotation_day: rotationDay,\n  day_name: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][currentDay],\n  timestamp: new Date().toISOString()\n};"
      },
      "id": "god-config-001",
      "name": "GOD Digital Configuration",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [-2200, 50]
    },
    {
      "parameters": {
        "jsCode": "// Strategic Content Calendar Selector\nconst config = $input.first().json;\nconst todaysStrategy = config.todays_strategy;\nconst rotationDay = config.rotation_day;\n\n// Content templates based on rotation day\nconst contentTemplates = {\n  1: { // Educational Monday\n    hooks: [\n      '🎯 Want to master digital marketing in 2024?',\n      '💡 The #1 marketing mistake 90% of businesses make',\n      '🚀 How to 10X your marketing ROI with this simple strategy',\n      '⚡ The secret marketing tactic industry leaders don\\'t want you to know'\n    ],\n    content_angles: [\n      'Complete guide to modern digital marketing strategies that actually work',\n      'Step-by-step blueprint to building a marketing system that generates consistent leads',\n      'Advanced automation techniques that save 20+ hours per week while increasing results',\n      'Data-driven marketing strategies used by Fortune 500 companies'\n    ],\n    lead_magnets: [\n      'Free Digital Marketing Masterclass + Strategy Template',\n      'Complete Marketing Automation Blueprint (Worth $2,997)',\n      'Advanced Analytics Dashboard Template + Setup Guide',\n      'Marketing ROI Calculator + Optimization Checklist'\n    ]\n  },\n  2: { // Achievement Tuesday\n    hooks: [\n      '🏆 Just helped another client achieve 847% ROI increase',\n      '💰 Client Results: $2.3M in revenue generated in 90 days',\n      '📈 Case Study: How we transformed a struggling business into market leader',\n      '🎉 Another success story: 500% lead increase in 60 days'\n    ],\n    content_angles: [\n      'Behind-the-scenes look at our latest client transformation',\n      'Real case study: Complete marketing overhaul that changed everything',\n      'Client spotlight: From $10K to $100K monthly revenue in 6 months',\n      'Success story: How we helped a startup become industry leader'\n    ],\n    lead_magnets: [\n      'Free Business Growth Audit + Strategy Session',\n      'Case Study Collection: 50+ Client Success Stories',\n      'Revenue Growth Calculator + Action Plan',\n      'Free Marketing Performance Analysis'\n    ]\n  },\n  3: { // Psychology Wednesday\n    hooks: [\n      '🧠 The psychology trick that increases conversions by 340%',\n      '⚠️ Your competitors are using this against you RIGHT NOW',\n      '🔥 Limited Time: The marketing secret that\\'s changing everything',\n      '💥 Why 95% of businesses fail at marketing (and how to be the 5%)'\n    ],\n    content_angles: [\n      'Psychological triggers that make customers buy immediately',\n      'The hidden marketing psychology your competitors don\\'t understand',\n      'Scarcity and urgency tactics that ethical marketers use to drive action',\n      'Consumer behavior insights that transform marketing performance'\n    ],\n    lead_magnets: [\n      'Psychology-Based Marketing Playbook (Limited Access)',\n      'Conversion Psychology Checklist + Templates',\n      'Exclusive: Advanced Persuasion Techniques Guide',\n      'Marketing Psychology Masterclass (48-Hour Access)'\n    ]\n  },\n  4: { // Trends Thursday\n    hooks: [\n      '🔮 2024 Marketing Predictions: What\\'s Coming Next',\n      '📊 Breaking: New algorithm changes affecting 90% of businesses',\n      '🌟 The emerging trend that will dominate marketing in 2024',\n      '⚡ Industry Alert: Major shift happening in digital marketing'\n    ],\n    content_angles: [\n      'Latest industry trends and what they mean for your business',\n      'Future of digital marketing: Trends to watch and prepare for',\n      'Breaking down the latest algorithm updates and their impact',\n      'Emerging technologies reshaping the marketing landscape'\n    ],\n    lead_magnets: [\n      '2024 Marketing Trends Report + Action Plan',\n      'Future-Proof Marketing Strategy Guide',\n      'Industry Trends Analysis + Implementation Roadmap',\n      'Exclusive Trend Insights + Competitive Advantage Guide'\n    ]\n  },\n  5: { // Free Resource Friday\n    hooks: [\n      '🎁 FREE: Complete marketing automation workflow (Worth $5,000)',\n      '💝 Friday Freebie: Advanced n8n workflow for lead generation',\n      '🆓 Giving away our secret marketing templates today only',\n      '🎉 Free Resource Friday: Tools that transformed our agency'\n    ],\n    content_angles: [\n      'Complete collection of marketing templates and tools',\n      'Advanced automation workflows you can implement today',\n      'Free resources that have generated millions in revenue',\n      'Community-exclusive tools and templates for business growth'\n    ],\n    lead_magnets: [\n      'Complete Marketing Toolkit (50+ Templates & Tools)',\n      'Advanced n8n Automation Workflows Collection',\n      'Marketing Resource Library (Lifetime Access)',\n      'Free Strategy Session + Custom Workflow Setup'\n    ]\n  }\n};\n\nconst template = contentTemplates[rotationDay];\nconst selectedHook = template.hooks[Math.floor(Math.random() * template.hooks.length)];\nconst selectedAngle = template.content_angles[Math.floor(Math.random() * template.content_angles.length)];\nconst selectedMagnet = template.lead_magnets[Math.floor(Math.random() * template.lead_magnets.length)];\n\n// Generate CTAs based on content type\nconst ctas = {\n  educational: [\n    'Comment \"LEARN\" below for instant access to our free masterclass',\n    'DM us \"GUIDE\" to get the complete strategy blueprint',\n    'Save this post and share with someone who needs this',\n    'Click the link in bio for the full training'\n  ],\n  achievements: [\n    'Want similar results? DM us \"SUCCESS\" for a free strategy call',\n    'Comment \"RESULTS\" below to see how we can help your business',\n    'Ready for your transformation? Book a free consultation',\n    'DM \"GROWTH\" to discuss your business goals'\n  ],\n  marketing_psychology: [\n    'Comment \"PSYCHOLOGY\" for the complete playbook',\n    'DM \"CONVERT\" to access our advanced conversion strategies',\n    'Limited spots available - comment \"EXCLUSIVE\" below',\n    'Only sharing this with serious business owners - DM \"SERIOUS\"'\n  ],\n  trends_news: [\n    'Stay ahead of the curve - comment \"TRENDS\" for the full report',\n    'DM \"FUTURE\" to get our 2024 marketing predictions',\n    'Comment \"INSIGHTS\" for exclusive industry analysis',\n    'Get the competitive advantage - DM \"ADVANTAGE\"'\n  ],\n  free_resources: [\n    'Comment \"FREE\" below for instant download access',\n    'DM \"RESOURCES\" to get the complete toolkit',\n    'Limited time offer - comment \"TOOLKIT\" now',\n    'Join our community for exclusive resources - link in bio'\n  ]\n};\n\nconst selectedCTA = ctas[todaysStrategy.type][Math.floor(Math.random() * ctas[todaysStrategy.type].length)];\n\nreturn {\n  content_type: todaysStrategy.type,\n  content_focus: todaysStrategy.focus,\n  content_goal: todaysStrategy.goal,\n  psychological_trigger: todaysStrategy.psychological_trigger,\n  content_pillars: todaysStrategy.content_pillars,\n  hook: selectedHook,\n  content_angle: selectedAngle,\n  lead_magnet: selectedMagnet,\n  cta_strategy: selectedCTA,\n  rotation_day: rotationDay,\n  strategy_selected: true,\n  timestamp: new Date().toISOString()\n};"
      },
      "id": "content-calendar-001",
      "name": "Strategic Content Calendar",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [-1800, 50]
    },
    {
      "parameters": {
        "url": "https://trends.google.com/trends/trendingsearches/daily/rss?geo=US",
        "options": {}
      },
      "id": "trends-research-001",
      "name": "Industry Trends Research",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [-1600, 150]
    },
    {
      "parameters": {
        "jsCode": "// Advanced Trend Analysis for GOD Digital Marketing\nconst trendData = $input.first()?.json || {};\nconst config = $('GOD Digital Configuration').item.json;\nconst contentStrategy = $('Strategic Content Calendar').item.json;\n\n// Extract trending topics from Google Trends\nlet googleTrends = [];\ntry {\n  if (trendData.rss && trendData.rss.channel && trendData.rss.channel.item) {\n    const items = Array.isArray(trendData.rss.channel.item) ? trendData.rss.channel.item : [trendData.rss.channel.item];\n    googleTrends = items.slice(0, 10).map(item => ({\n      title: item.title,\n      traffic: item['ht:approx_traffic'] || 'Unknown',\n      news_items: item['ht:news_item'] || []\n    }));\n  }\n} catch (error) {\n  console.log('Error parsing Google trends:', error.message);\n}\n\n// Industry-specific trending topics for digital marketing\nconst digitalMarketingTrends = [\n  'AI Marketing Automation 2024', 'ChatGPT for Business', 'TikTok Marketing Strategy',\n  'Google Analytics 4 Updates', 'iOS Privacy Changes Impact', 'Voice Search Optimization',\n  'Video Marketing Trends', 'Influencer Marketing ROI', 'Marketing Attribution Models',\n  'Customer Data Platforms', 'Programmatic Advertising', 'Marketing Automation Tools',\n  'Social Commerce Growth', 'Personalization at Scale', 'Zero-Party Data Strategy'\n];\n\n// Content-type specific trends\nconst contentTypeTrends = {\n  educational: ['Marketing Best Practices', 'Digital Strategy Guide', 'ROI Optimization', 'Lead Generation Tactics'],\n  achievements: ['Client Success Stories', 'Case Study Results', 'Business Transformation', 'Revenue Growth'],\n  marketing_psychology: ['Consumer Behavior', 'Conversion Psychology', 'Persuasion Techniques', 'Buyer Journey'],\n  trends_news: ['Marketing Innovation', 'Industry Disruption', 'Future Predictions', 'Technology Adoption'],\n  free_resources: ['Marketing Templates', 'Free Tools', 'Resource Libraries', 'Community Value']\n};\n\n// Combine all trends and prioritize based on content type\nconst relevantTrends = [\n  ...googleTrends.map(t => t.title),\n  ...digitalMarketingTrends,\n  ...contentTypeTrends[contentStrategy.content_type] || []\n];\n\n// Select top trends for content creation\nconst selectedTrends = relevantTrends.slice(0, 20);\n\n// Generate trend-based keywords\nconst trendKeywords = selectedTrends.map(trend => \n  trend.toLowerCase().split(' ').slice(0, 3).join(' ')\n).slice(0, 10);\n\nreturn {\n  google_trends: googleTrends,\n  digital_marketing_trends: digitalMarketingTrends,\n  content_type_trends: contentTypeTrends[contentStrategy.content_type],\n  selected_trends: selectedTrends,\n  trend_keywords: trendKeywords,\n  content_type: contentStrategy.content_type,\n  trend_analysis_complete: true,\n  timestamp: new Date().toISOString()\n};"
      },
      "id": "trend-analyzer-001",
      "name": "Advanced Trend Analyzer",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [-1400, 150]
    },
    {
      "parameters": {
        "promptType": "define",
        "text": "=You are the world's leading AI marketing strategist for GOD Digital Marketing, specializing in creating viral, conversion-focused content that generates massive engagement and leads.\n\nTODAY'S MISSION: Create {{ $('Strategic Content Calendar').item.json.content_type }} content that {{ $('Strategic Content Calendar').item.json.content_goal }}.\n\nCONTEXT:\n• Company: {{ $('GOD Digital Configuration').item.json.company_name }}\n• Services: {{ JSON.stringify($('GOD Digital Configuration').item.json.services_offered) }}\n• Value Prop: {{ $('GOD Digital Configuration').item.json.value_proposition }}\n• Target Audience: {{ $('GOD Digital Configuration').item.json.target_audience }}\n• Content Focus: {{ $('Strategic Content Calendar').item.json.content_focus }}\n• Psychological Trigger: {{ $('Strategic Content Calendar').item.json.psychological_trigger }}\n• Content Pillars: {{ $('Strategic Content Calendar').item.json.content_pillars }}\n• Trending Topics: {{ $('Advanced Trend Analyzer').item.json.selected_trends }}\n\nCONTENT STRATEGY:\n• Hook: {{ $('Strategic Content Calendar').item.json.hook }}\n• Angle: {{ $('Strategic Content Calendar').item.json.content_angle }}\n• Lead Magnet: {{ $('Strategic Content Calendar').item.json.lead_magnet }}\n• CTA: {{ $('Strategic Content Calendar').item.json.cta_strategy }}\n\nCREATE PROFESSIONAL CONTENT FOR:\n1. FACEBOOK_POST: Engaging, story-driven, community-focused (include link to https://godigitalmarketing.com)\n2. INSTAGRAM_CAPTION: Visual storytelling, hashtag-optimized, action-oriented\n3. INSTAGRAM_STORY: Quick, swipeable, compelling CTA\n4. LINKEDIN_POST: Professional, thought leadership, B2B focused\n5. TWITTER_THREAD: Educational, viral potential, 7-10 tweets\n6. TWITTER_SINGLE: Punchy, retweetable, trending\n7. YOUTUBE_TITLE: SEO-optimized, click-worthy, under 60 characters\n8. YOUTUBE_DESCRIPTION: Detailed, timestamp-rich, link-optimized\n9. TIKTOK_SCRIPT: Trending, educational, hook-heavy\n10. PINTEREST_TITLE: SEO-focused, keyword-rich\n11. PINTEREST_DESCRIPTION: Search-optimized, actionable\n12. REDDIT_TITLE: Community-friendly, discussion-starter\n13. REDDIT_POST: Value-first, authentic, helpful\n14. DISCORD_MESSAGE: Community-focused, engaging\n15. TELEGRAM_MESSAGE: Direct, actionable, link-optimized\n\nREQUIREMENTS:\n• Include https://godigitalmarketing.com strategically\n• Use specific numbers, results, and case studies\n• Avoid AI-sounding phrases - write naturally\n• Make each post conversion-focused with strong CTAs\n• Include relevant hashtags for each platform\n• Focus on {{ $('Strategic Content Calendar').item.json.psychological_trigger }}\n• Incorporate trending topics naturally\n• Maintain GOD Digital Marketing's authoritative yet approachable voice\n• Each post should drive action and build community\n\nFormat as JSON with all platform keys. Make it irresistible and results-driven."
      },
      "type": "@n8n/n8n-nodes-langchain.chainLlm",
      "typeVersion": 1.6,
      "position": [-1200, 50],
      "id": "master-content-ai-001",
      "name": "Master Content Creator AI"
    },
    {
      "parameters": {
        "model": "meta-llama/llama-3.1-70b-versatile",
        "options": {
          "temperature": 0.7,
          "maxTokens": 4000
        }
      },
      "type": "@n8n/n8n-nodes-langchain.lmChatGroq",
      "typeVersion": 1,
      "position": [-1200, 200],
      "id": "groq-ai-model-001",
      "name": "Advanced Groq AI Model",
      "credentials": {
        "groqApi": {
          "id": "groq-api-key",
          "name": "Groq API"
        }
      }
    }
  ],
    {
      "parameters": {
        "jsCode": "// Ultimate Content Processor for GOD Digital Marketing\nconst aiResponse = $input.first()?.json?.response || $input.first()?.json?.output || '';\nconst strategy = $('Strategic Content Calendar').item.json;\nconst config = $('GOD Digital Configuration').item.json;\nconst trends = $('Advanced Trend Analyzer').item.json;\n\nlet socialContent = {};\n\n// Try to extract JSON from AI response\ntry {\n  const jsonMatch = aiResponse.match(/\\{[\\s\\S]*\\}/);\n  if (jsonMatch) {\n    socialContent = JSON.parse(jsonMatch[0]);\n  }\n} catch (error) {\n  console.log('AI content parsing failed, generating premium fallback content');\n}\n\n// Premium fallback content generation based on content type\nif (Object.keys(socialContent).length === 0) {\n  const hook = strategy.hook;\n  const contentAngle = strategy.content_angle;\n  const leadMagnet = strategy.lead_magnet;\n  const cta = strategy.cta_strategy;\n  const hashtags = config.hashtag_sets;\n  const contentType = strategy.content_type;\n  \n  // Get appropriate hashtags for content type\n  const typeHashtags = hashtags[contentType] || hashtags.general;\n  \n  socialContent = {\n    facebook_post: `${hook}\\n\\n${contentAngle}\\n\\n🎁 EXCLUSIVE: ${leadMagnet}\\n\\n${cta}\\n\\nWhat's your biggest challenge with ${contentType === 'educational' ? 'digital marketing' : 'business growth'}? Share below! 👇\\n\\nLearn more: https://godigitalmarketing.com\\n\\n${typeHashtags}`,\n    \n    instagram_caption: `${hook} ✨\\n\\n${contentAngle}\\n\\n🔥 ${leadMagnet}\\n\\n${cta}\\n\\n💭 Save this post and share with someone who needs this!\\n\\n🔗 Link in bio: https://godigitalmarketing.com\\n\\n${typeHashtags} ${hashtags.general}`,\n    \n    instagram_story: `${hook}\\n\\n${contentAngle.substring(0, 120)}...\\n\\n🔥 ${leadMagnet}\\n\\nSwipe up to learn more! 👆`,\n    \n    linkedin_post: `${hook}\\n\\n${contentAngle}\\n\\n🎯 Key Insight: ${contentType === 'educational' ? 'Most businesses are missing this crucial element in their marketing strategy' : 'Success leaves clues - here\\'s what we\\'ve learned'}.\\n\\n💡 ${leadMagnet}\\n\\n${cta}\\n\\nWhat's been your experience with this? Share your thoughts below.\\n\\n🔗 https://godigitalmarketing.com\\n\\n${typeHashtags.replace(/#/g, '')}`,\n    \n    twitter_thread: `🧵 THREAD: ${hook} (1/8)\\n\\n${contentAngle}\\n\\nHere's what ${contentType === 'educational' ? 'most businesses get wrong' : 'we\\'ve discovered'}... 👇\\n\\n2/8 ${contentAngle.split('.')[0]}...`,\n    \n    twitter_single: `${hook}\\n\\n${contentAngle.substring(0, 180)}...\\n\\n${cta}\\n\\n🔗 https://godigitalmarketing.com\\n\\n${typeHashtags}`,\n    \n    youtube_title: `${hook.replace(/🚀|⚡|🎯|💡|🧠|🏆|💰|📈|🎉|🔮|📊|🌟|🎁|💝|🆓/, '')} | ${contentAngle.split('.')[0]}`,\n    \n    youtube_description: `${contentAngle}\\n\\n🎁 ${leadMagnet}\\n\\n${cta}\\n\\n⏰ TIMESTAMPS:\\n0:00 Introduction\\n1:30 The Challenge\\n3:00 Our Solution\\n5:30 Implementation\\n7:00 Results\\n8:30 Next Steps\\n\\n🔗 USEFUL LINKS:\\n• Free Resources: https://godigitalmarketing.com\\n• Book a Strategy Call: https://godigitalmarketing.com/call\\n• Follow Us: https://godigitalmarketing.com/social\\n\\n${typeHashtags}\\n\\n---\\nGOD Digital Marketing - Transforming Businesses Through AI-Powered Solutions\\n#GODDigitalMarketing #DigitalTransformation`,\n    \n    tiktok_script: `Hook: ${hook}\\nProblem: ${contentAngle.split('.')[0]}\\nSolution: ${contentAngle.split('.').slice(1).join('.')}\\nProof: Real client results\\nCTA: ${cta}\\nLink: https://godigitalmarketing.com\\nHashtags: ${typeHashtags}`,\n    \n    pinterest_title: `${contentAngle.split('.')[0]} | ${leadMagnet} | GOD Digital Marketing`,\n    \n    pinterest_description: `${contentAngle}\\n\\n✅ ${leadMagnet}\\n\\n${cta}\\n\\n🔗 https://godigitalmarketing.com\\n\\n${hashtags.business} ${hashtags.general}`,\n    \n    reddit_title: hook.replace(/🚀|⚡|🎯|💡|🧠|🏆|💰|📈|🎉|🔮|📊|🌟|🎁|💝|🆓/, '').trim(),\n    \n    reddit_post: `${contentAngle}\\n\\n**${leadMagnet}**\\n\\n${cta}\\n\\nWhat's been your experience with this? Would love to hear your thoughts and answer any questions!\\n\\nMore resources: https://godigitalmarketing.com`,\n    \n    discord_message: `🚀 **${hook}**\\n\\n${contentAngle}\\n\\n💡 **${leadMagnet}**\\n\\n${cta}\\n\\n🔗 https://godigitalmarketing.com\\n\\n@everyone What do you think about this strategy?`,\n    \n    telegram_message: `🎯 ${hook}\\n\\n${contentAngle}\\n\\n🎁 ${leadMagnet}\\n\\n${cta}\\n\\n🔗 https://godigitalmarketing.com\\n\\nJoin our community for exclusive content and resources!`\n  };\n}\n\n// Advanced Quality Assessment\nconst qualityMetrics = {\n  content_length_check: Object.values(socialContent).every(content => \n    content && content.length >= config.quality_thresholds.min_content_length && \n    content.length <= config.quality_thresholds.max_content_length\n  ),\n  platform_coverage: Object.keys(socialContent).length >= config.quality_thresholds.required_platforms,\n  cta_presence: Object.values(socialContent).every(content => \n    content && (content.includes('DM') || content.includes('Comment') || content.includes('Click') || content.includes('Visit') || content.includes('Book'))\n  ),\n  website_link: Object.values(socialContent).every(content => \n    content && content.includes('godigitalmarketing.com')\n  ),\n  engagement_elements: Object.values(socialContent).every(content => \n    content && (content.includes('?') || content.includes('👇') || content.includes('comment') || content.includes('share'))\n  ),\n  brand_consistency: Object.values(socialContent).every(content => \n    content && (content.includes('GOD') || content.includes('Digital') || content.includes('Marketing'))\n  )\n};\n\nconst qualityScore = Object.values(qualityMetrics).filter(Boolean).length / Object.keys(qualityMetrics).length * 10;\nconst qualityPass = qualityScore >= config.quality_thresholds.min_engagement_score;\n\nreturn {\n  ...socialContent,\n  quality_metrics: {\n    ...qualityMetrics,\n    overall_score: qualityScore,\n    quality_pass: qualityPass,\n    platforms_ready: Object.keys(socialContent).length\n  },\n  content_type: strategy.content_type,\n  hook: strategy.hook,\n  content_angle: strategy.content_angle,\n  lead_magnet: strategy.lead_magnet,\n  cta_strategy: strategy.cta_strategy,\n  primary_keyword: trends.trend_keywords[0] || 'digital marketing',\n  processing_complete: true,\n  timestamp: new Date().toISOString()\n};"
      },
      "id": "ultimate-content-processor-001",
      "name": "Ultimate Content Processor",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [-1000, 50]
    }
  ],
  "connections": {
    "Smart Daily Scheduler": {
      "main": [
        [
          {
            "node": "GOD Digital Configuration",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Manual Test Trigger": {
      "main": [
        [
          {
            "node": "GOD Digital Configuration",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "GOD Digital Configuration": {
      "main": [
        [
          {
            "node": "Strategic Content Calendar",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Strategic Content Calendar": {
      "main": [
        [
          {
            "node": "Industry Trends Research",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Industry Trends Research": {
      "main": [
        [
          {
            "node": "Advanced Trend Analyzer",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Advanced Trend Analyzer": {
      "main": [
        [
          {
            "node": "Master Content Creator AI",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Master Content Creator AI": {
      "main": [
        [
          {
            "node": "Ultimate Content Processor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Advanced Groq AI Model": {
      "ai_languageModel": [
        [
          {
            "node": "Master Content Creator AI",
            "type": "ai_languageModel",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "meta": {
    "templateCredsSetupCompleted": true,
    "instanceId": "god-digital-ultimate-workflow"
  }
}
