{"nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 9,14,18 * * 1,2,3,4,5"}]}}, "id": "80c5c27f-d2f7-4b5d-a68c-2af249e9c199", "name": "Smart Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [-2100, -40]}, {"parameters": {}, "id": "1e600594-822b-4415-9ee7-e3e8e9789d78", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-2100, 60]}, {"parameters": {"url": "https://api.github.com/repos/yourusername/social-content-backup/contents/config.json", "authentication": "predefinedCredentialType", "nodeCredentialType": "githubApi", "options": {}}, "id": "config-loader-001", "name": "Load Configuration", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-1900, 20]}, {"parameters": {"jsCode": "// Enhanced Configuration Setup with Error Handling\nconst defaultConfig = {\n  business_type: 'Digital Marketing Agency',\n  target_audience: 'Small to Medium Business Owners',\n  value_proposition: 'AI-Powered Marketing Automation That Generates 300% More Leads',\n  services_offered: 'SEO, Social Media Marketing, Lead Generation, Marketing Automation, Content Creation',\n  brand_voice: 'Professional yet approachable, data-driven, results-focused',\n  posting_times: {\n    facebook: ['09:00', '15:00', '19:00'],\n    instagram: ['11:00', '14:00', '17:00', '20:00'],\n    linkedin: ['08:00', '12:00', '17:00'],\n    twitter: ['09:00', '13:00', '16:00', '20:00'],\n    pinterest: ['20:00', '21:00'],\n    youtube: ['18:00', '19:00'],\n    tiktok: ['18:00', '19:00', '21:00']\n  },\n  content_themes: [\n    'Lead Generation Strategies',\n    'Marketing Automation Tips',\n    'Business Growth Hacks',\n    'AI in Marketing',\n    'ROI Optimization',\n    'Customer Acquisition',\n    'Social Media Strategy'\n  ],\n  hashtag_sets: {\n    general: '#DigitalMarketing #BusinessGrowth #MarketingTips #LeadGeneration #MarketingAutomation',\n    ai: '#AIMarketing #MarketingAI #AutomationTools #TechMarketing #DigitalTransformation',\n    business: '#SmallBusiness #Entrepreneur #BusinessTips #GrowthHacking #BusinessStrategy',\n    results: '#MarketingROI #ResultsDriven #MarketingResults #BusinessSuccess #GrowthMetrics'\n  },\n  quality_thresholds: {\n    min_content_length: 50,\n    max_content_length: 2000,\n    min_engagement_score: 7,\n    required_platforms: 5\n  }\n};\n\ntry {\n  // Try to load configuration from external source\n  const configData = $input.first()?.json;\n  const loadedConfig = configData ? JSON.parse(Buffer.from(configData.content, 'base64').toString()) : {};\n  \n  // Merge with defaults\n  const finalConfig = { ...defaultConfig, ...loadedConfig };\n  \n  return {\n    ...finalConfig,\n    config_loaded: true,\n    config_source: configData ? 'external' : 'default',\n    timestamp: new Date().toISOString()\n  };\n} catch (error) {\n  // Fallback to default configuration\n  return {\n    ...defaultConfig,\n    config_loaded: true,\n    config_source: 'default_fallback',\n    config_error: error.message,\n    timestamp: new Date().toISOString()\n  };\n}"}, "id": "b9b22ff3-53c7-4b96-94aa-a3a313e62318", "name": "Enhanced Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1680, 20]}, {"parameters": {"url": "https://trends.google.com/trends/trendingsearches/daily/rss?geo=US", "options": {}}, "id": "google-trends-001", "name": "Fetch Google Trends", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-1460, 160]}, {"parameters": {"jsCode": "// Enhanced Trend Analysis with Multiple Sources\nconst trendData = $input.first()?.json || {};\nconst config = $('Enhanced Configuration').item.json;\n\n// Extract trending topics from Google Trends RSS\nlet trendingTopics = [];\ntry {\n  if (trendData.rss && trendData.rss.channel && trendData.rss.channel.item) {\n    const items = Array.isArray(trendData.rss.channel.item) ? trendData.rss.channel.item : [trendData.rss.channel.item];\n    trendingTopics = items.slice(0, 10).map(item => ({\n      title: item.title,\n      traffic: item['ht:approx_traffic'] || 'Unknown',\n      news_items: item['ht:news_item'] || []\n    }));\n  }\n} catch (error) {\n  console.log('Error parsing trends:', error.message);\n}\n\n// Add industry-specific trending topics\nconst industryTrends = [\n  'AI Marketing Automation',\n  'Lead Generation 2024',\n  'Social Media ROI',\n  'Digital Marketing Trends',\n  'Business Automation Tools',\n  'Customer Acquisition Cost',\n  'Marketing Attribution',\n  'Conversion Rate Optimization'\n];\n\n// Combine and prioritize trends\nconst allTrends = [\n  ...trendingTopics.map(t => t.title),\n  ...industryTrends,\n  ...config.content_themes\n];\n\n// Select best trends for content creation\nconst selectedTrends = allTrends.slice(0, 15);\n\nreturn {\n  trending_topics: selectedTrends,\n  google_trends: trendingTopics,\n  industry_trends: industryTrends,\n  content_themes: config.content_themes,\n  trend_analysis_complete: true,\n  timestamp: new Date().toISOString()\n};"}, "id": "22762b3c-726f-4e3b-981a-68639b13e18d", "name": "Enhanced Trend Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1220, 160]}, {"parameters": {"jsCode": "// Advanced Content Strategy Processor with A/B Testing\nconst aiResponse = $input.first()?.json?.response || $input.first()?.json?.output || '';\nconst config = $('Enhanced Configuration').item.json;\nconst trends = $('Enhanced Trend Analysis').item.json;\n\nlet contentIdeas = [];\n\ntry {\n  // Try to extract JSON from AI response\n  const jsonMatch = aiResponse.match(/\\[.*\\]/s) || aiResponse.match(/\\{.*\\}/s);\n  if (jsonMatch) {\n    const parsed = JSON.parse(jsonMatch[0]);\n    contentIdeas = Array.isArray(parsed) ? parsed : [parsed];\n  }\n} catch (error) {\n  console.log('JSON parsing failed, using fallback content');\n}\n\n// Enhanced fallback content with multiple variations\nif (contentIdeas.length === 0) {\n  const fallbackIdeas = [\n    {\n      hook: `🚀 Want to 3X your business revenue in 90 days?`,\n      content_angle: `The exact 5-step system our clients use to generate $50K+ in new revenue`,\n      lead_magnet: `Free Revenue Growth Blueprint + 1-on-1 Strategy Call`,\n      cta_strategy: `Comment 'BLUEPRINT' below for instant access`,\n      best_platforms: ['LinkedIn', 'Facebook', 'Instagram', 'Twitter'],\n      pain_point_addressed: 'Struggling with inconsistent revenue and lead generation',\n      content_type: 'case_study',\n      engagement_score: 9\n    },\n    {\n      hook: `⚡ Stop wasting money on marketing that doesn't work`,\n      content_angle: `How we helped 200+ businesses cut marketing costs by 60% while doubling leads`,\n      lead_magnet: `Free Marketing Audit + ROI Calculator`,\n      cta_strategy: `DM 'AUDIT' to get your free marketing analysis`,\n      best_platforms: ['LinkedIn', 'Facebook', 'YouTube'],\n      pain_point_addressed: 'High marketing costs with poor ROI',\n      content_type: 'problem_solution',\n      engagement_score: 8\n    },\n    {\n      hook: `🎯 The #1 mistake killing your lead generation`,\n      content_angle: `Why 90% of businesses fail at lead gen (and the simple fix that changes everything)`,\n      lead_magnet: `Lead Generation Checklist + Templates`,\n      cta_strategy: `Save this post and comment 'LEADS' for the free checklist`,\n      best_platforms: ['Instagram', 'TikTok', 'LinkedIn'],\n      pain_point_addressed: 'Poor lead quality and low conversion rates',\n      content_type: 'educational',\n      engagement_score: 8\n    }\n  ];\n  contentIdeas = fallbackIdeas;\n}\n\n// Select best content based on current trends and engagement potential\nconst selectedContent = contentIdeas.reduce((best, current) => {\n  const currentScore = (current.engagement_score || 7) + \n    (trends.trending_topics.some(topic => \n      current.content_angle.toLowerCase().includes(topic.toLowerCase().split(' ')[0])\n    ) ? 2 : 0);\n  const bestScore = (best.engagement_score || 7) + \n    (trends.trending_topics.some(topic => \n      best.content_angle.toLowerCase().includes(topic.toLowerCase().split(' ')[0])\n    ) ? 2 : 0);\n  return currentScore > bestScore ? current : best;\n});\n\n// Generate A/B test variations\nconst abTestVariations = {\n  hook_a: selectedContent.hook,\n  hook_b: selectedContent.hook.replace(/🚀|⚡|🎯/, '💡').replace(/Want to|Stop|The #1/, 'Ready to'),\n  cta_a: selectedContent.cta_strategy,\n  cta_b: selectedContent.cta_strategy.replace(/Comment|DM|Save/, 'Click the link in bio and')\n};\n\nreturn {\n  all_content_ideas: contentIdeas,\n  selected_content: selectedContent,\n  hook: selectedContent.hook,\n  content_angle: selectedContent.content_angle,\n  lead_magnet: selectedContent.lead_magnet,\n  cta_strategy: selectedContent.cta_strategy,\n  best_platforms: selectedContent.best_platforms,\n  pain_point: selectedContent.pain_point_addressed,\n  content_type: selectedContent.content_type || 'general',\n  engagement_score: selectedContent.engagement_score || 7,\n  ab_test_variations: abTestVariations,\n  primary_keyword: selectedContent.content_angle.split(' ').slice(0, 3).join(' '),\n  processing_timestamp: new Date().toISOString()\n};"}, "id": "9df83291-8e00-4afc-9114-70358f83b9d7", "name": "Advanced Content Strategy", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-800, 20]}, {"parameters": {"extractionValues": {"values": [{"key": "titles", "cssSelector": "h3", "returnArray": true}, {"key": "snippets", "cssSelector": ".VwiC3b", "returnArray": true}]}, "options": {}}, "id": "c7b2aed7-44a3-47b2-95b1-bd0d7ee14215", "name": "Extract Competitors", "type": "n8n-nodes-base.htmlExtract", "typeVersion": 1, "position": [-560, 180]}, {"parameters": {"jsCode": "// Advanced Multi-Platform Content Processor with Quality Assurance\nconst aiResponse = $input.first()?.json?.response || $input.first()?.json?.output || '';\nconst strategy = $('Advanced Content Strategy').item.json;\nconst config = $('Enhanced Configuration').item.json;\n\nlet socialContent = {};\n\ntry {\n  // Try to extract JSON from AI response\n  const jsonMatch = aiResponse.match(/\\{[\\s\\S]*\\}/);\n  if (jsonMatch) {\n    socialContent = JSON.parse(jsonMatch[0]);\n  }\n} catch (error) {\n  console.log('AI content parsing failed, generating fallback content');\n}\n\n// Enhanced fallback content generation\nif (Object.keys(socialContent).length === 0) {\n  const hook = strategy.hook;\n  const contentAngle = strategy.content_angle;\n  const leadMagnet = strategy.lead_magnet;\n  const cta = strategy.cta_strategy;\n  const hashtags = config.hashtag_sets;\n  \n  socialContent = {\n    facebook_post: `${hook}\\n\\n${contentAngle}\\n\\n🎁 EXCLUSIVE: ${leadMagnet}\\n\\n${cta}\\n\\nWhat's your biggest marketing challenge? Let me know in the comments! 👇\\n\\n${hashtags.general}`,\n    \n    instagram_caption: `${hook} ✨\\n\\n${contentAngle}\\n\\n🔥 ${leadMagnet}\\n\\n${cta}\\n\\n💭 Save this post for later and share with a business owner who needs this!\\n\\n${hashtags.general} ${hashtags.business}`,\n    \n    instagram_story: `${hook}\\n\\n${contentAngle.substring(0, 100)}...\\n\\nSwipe up for the full guide! 👆`,\n    \n    linkedin_post: `${hook}\\n\\n${contentAngle}\\n\\n🎯 Key Takeaway: Most businesses are missing this crucial element in their marketing strategy.\\n\\nInterested in ${leadMagnet.toLowerCase()}?\\n\\n${cta}\\n\\nWhat's been your experience with lead generation? Share your thoughts below.\\n\\n${hashtags.general.replace('#', '')}`,\n    \n    twitter_thread: `🧵 THREAD: ${hook} (1/7)\\n\\n${contentAngle}\\n\\nHere's what most businesses get wrong... 👇`,\n    \n    twitter_single: `${hook}\\n\\n${contentAngle.substring(0, 150)}...\\n\\n${cta}\\n\\n${hashtags.general}`,\n    \n    youtube_title: `${hook.replace(/🚀|⚡|🎯|💡/, '')} | ${contentAngle.split('.')[0]}`,\n    \n    youtube_description: `${contentAngle}\\n\\n🎁 ${leadMagnet}\\n${cta}\\n\\n⏰ TIMESTAMPS:\\n0:00 Introduction\\n1:30 The Problem\\n3:00 The Solution\\n5:30 Implementation\\n7:00 Results\\n8:30 Next Steps\\n\\n🔗 LINKS:\\n• Free Resources: https://godigitalmarketing.com\\n• Book a Call: https://godigitalmarketing.com/call\\n\\n${hashtags.general}`,\n    \n    tiktok_script: `Hook: ${hook}\\nProblem: ${contentAngle.split('.')[0]}\\nSolution: ${contentAngle.split('.').slice(1).join('.')}\\nProof: Real client results\\nCTA: ${cta}\\nHashtags: ${hashtags.general}`,\n    \n    pinterest_title: `${contentAngle.split('.')[0]} | ${leadMagnet}`,\n    \n    pinterest_description: `${contentAngle}\\n\\n✅ ${leadMagnet}\\n\\n${cta}\\n\\n${hashtags.business} ${hashtags.results}`,\n    \n    reddit_title: hook.replace(/🚀|⚡|🎯|💡/, '').trim(),\n    \n    reddit_post: `${contentAngle}\\n\\n**${leadMagnet}**\\n\\n${cta}\\n\\nWhat's been your experience with this? Would love to hear your thoughts!`,\n    \n    discord_message: `🚀 **${hook}**\\n\\n${contentAngle}\\n\\n💡 **${leadMagnet}**\\n\\n${cta}\\n\\n@everyone What do you think about this strategy?`,\n    \n    telegram_message: `🎯 ${hook}\\n\\n${contentAngle}\\n\\n🎁 ${leadMagnet}\\n\\n${cta}\\n\\nJoin our community for more tips: https://t.me/godigitalmarketing`\n  };\n}\n\n// Advanced Quality Assessment\nconst qualityMetrics = {\n  content_length_check: Object.values(socialContent).every(content => \n    content && content.length >= config.quality_thresholds.min_content_length && \n    content.length <= config.quality_thresholds.max_content_length\n  ),\n  platform_coverage: Object.keys(socialContent).length >= config.quality_thresholds.required_platforms,\n  cta_presence: Object.values(socialContent).every(content => \n    content && (content.includes('DM') || content.includes('Comment') || content.includes('Click') || content.includes('Visit'))\n  ),\n  value_proposition: Object.values(socialContent).every(content => \n    content && (content.includes(config.value_proposition.split(' ')[0]) || content.includes('result') || content.includes('benefit'))\n  ),\n  engagement_elements: Object.values(socialContent).every(content => \n    content && (content.includes('?') || content.includes('👇') || content.includes('comment') || content.includes('share'))\n  )\n};\n\nconst qualityScore = Object.values(qualityMetrics).filter(Boolean).length / Object.keys(qualityMetrics).length * 10;\nconst qualityPass = qualityScore >= config.quality_thresholds.min_engagement_score;\n\n// Content backup for failed posts\nconst contentBackup = {\n  timestamp: new Date().toISOString(),\n  original_content: socialContent,\n  strategy_used: strategy,\n  quality_score: qualityScore\n};\n\nreturn {\n  ...socialContent,\n  quality_metrics: {\n    ...qualityMetrics,\n    overall_score: qualityScore,\n    quality_pass: qualityPass,\n    platforms_ready: Object.keys(socialContent).length\n  },\n  content_backup: contentBackup,\n  hook: strategy.hook,\n  content_angle: strategy.content_angle,\n  lead_magnet: strategy.lead_magnet,\n  cta_strategy: strategy.cta_strategy,\n  primary_keyword: strategy.primary_keyword,\n  business_type: config.business_type,\n  processing_complete: true,\n  timestamp: new Date().toISOString()\n};"}, "id": "a77e631a-c97a-4ca0-b222-73768cbc87e4", "name": "Advanced Content Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-140, 20]}, {"parameters": {"url": "https://api.unsplash.com/search/photos", "authentication": "predefinedCredentialType", "nodeCredentialType": "unsplashApi", "sendQuery": true, "queryParameters": {"parameters": [{"name": "query", "value": "={{ $('Advanced Content Processor').item.json.primary_keyword }}"}, {"name": "per_page", "value": "5"}, {"name": "orientation", "value": "landscape"}, {"name": "content_filter", "value": "high"}, {"name": "order_by", "value": "relevant"}]}, "options": {}}, "id": "d8566701-6f07-4d62-a316-fabc79e3e596", "name": "Professional Image Search", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [80, -100]}, {"parameters": {"url": "https://api.pexels.com/v1/search", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "YOUR_PEXELS_API_KEY"}]}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "query", "value": "={{ $('Advanced Content Processor').item.json.primary_keyword }}"}, {"name": "per_page", "value": "5"}, {"name": "orientation", "value": "landscape"}]}, "options": {}}, "id": "pexels-backup-001", "name": "Pexels Backup Images", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [80, -40]}, {"parameters": {"jsCode": "// Enhanced Image Selection with Multiple Sources and Optimization\nconst unsplashData = $('Professional Image Search').item?.json || {};\nconst pexelsData = $('Pexels Backup Images').item?.json || {};\nconst contentData = $('Advanced Content Processor').item.json;\n\nlet selectedImage = null;\nlet imageSource = 'none';\n\n// Try Unsplash first\nif (unsplashData.results && unsplashData.results.length > 0) {\n  const images = unsplashData.results;\n  // Select image with best quality and relevance\n  selectedImage = images.find(img => \n    img.width >= 1200 && img.height >= 800 && \n    (img.alt_description?.toLowerCase().includes(contentData.primary_keyword.toLowerCase()) || \n     img.description?.toLowerCase().includes(contentData.primary_keyword.toLowerCase()))\n  ) || images[0];\n  imageSource = 'unsplash';\n}\n\n// Fallback to Pexels if Unsplash fails\nif (!selectedImage && pexelsData.photos && pexelsData.photos.length > 0) {\n  selectedImage = pexelsData.photos[0];\n  imageSource = 'pexels';\n}\n\n// Generate multiple image formats for different platforms\nif (selectedImage) {\n  const baseUrl = imageSource === 'unsplash' ? selectedImage.urls.regular : selectedImage.src.large;\n  const imageData = {\n    // Original high-quality image\n    image_url: baseUrl,\n    image_url_hd: imageSource === 'unsplash' ? selectedImage.urls.full : selectedImage.src.original,\n    \n    // Platform-optimized versions\n    facebook_image: baseUrl + '&w=1200&h=630&fit=crop', // Facebook recommended size\n    instagram_image: baseUrl + '&w=1080&h=1080&fit=crop', // Instagram square\n    instagram_story: baseUrl + '&w=1080&h=1920&fit=crop', // Instagram story\n    linkedin_image: baseUrl + '&w=1200&h=627&fit=crop', // LinkedIn recommended\n    twitter_image: baseUrl + '&w=1200&h=675&fit=crop', // Twitter card\n    pinterest_image: baseUrl + '&w=1000&h=1500&fit=crop', // Pinterest vertical\n    youtube_thumbnail: baseUrl + '&w=1280&h=720&fit=crop', // YouTube thumbnail\n    \n    // Image metadata\n    image_alt: imageSource === 'unsplash' ? \n      (selectedImage.alt_description || `${contentData.primary_keyword} marketing strategy`) :\n      `${contentData.primary_keyword} business growth`,\n    image_credit: imageSource === 'unsplash' ? \n      `Photo by ${selectedImage.user.name} on Unsplash` :\n      `Photo by ${selectedImage.photographer} on Pexels`,\n    image_source: imageSource,\n    image_id: imageSource === 'unsplash' ? selectedImage.id : selectedImage.id,\n    image_width: imageSource === 'unsplash' ? selectedImage.width : selectedImage.width,\n    image_height: imageSource === 'unsplash' ? selectedImage.height : selectedImage.height,\n    \n    // Download tracking (for Unsplash API compliance)\n    download_location: imageSource === 'unsplash' ? selectedImage.links.download_location : null,\n    \n    // Quality metrics\n    image_quality_score: (selectedImage.width >= 1200 && selectedImage.height >= 800) ? 10 : 7,\n    platform_ready: true\n  };\n  \n  return imageData;\n} else {\n  // Ultimate fallback - generate placeholder or use default brand image\n  return {\n    image_url: 'https://via.placeholder.com/1200x630/4F46E5/FFFFFF?text=God+Digital+Marketing',\n    image_url_hd: 'https://via.placeholder.com/1920x1080/4F46E5/FFFFFF?text=God+Digital+Marketing',\n    facebook_image: 'https://via.placeholder.com/1200x630/4F46E5/FFFFFF?text=Facebook+Post',\n    instagram_image: 'https://via.placeholder.com/1080x1080/4F46E5/FFFFFF?text=Instagram+Post',\n    instagram_story: 'https://via.placeholder.com/1080x1920/4F46E5/FFFFFF?text=Instagram+Story',\n    linkedin_image: 'https://via.placeholder.com/1200x627/4F46E5/FFFFFF?text=LinkedIn+Post',\n    twitter_image: 'https://via.placeholder.com/1200x675/4F46E5/FFFFFF?text=Twitter+Post',\n    pinterest_image: 'https://via.placeholder.com/1000x1500/4F46E5/FFFFFF?text=Pinterest+Pin',\n    youtube_thumbnail: 'https://via.placeholder.com/1280x720/4F46E5/FFFFFF?text=YouTube+Video',\n    image_alt: `${contentData.primary_keyword} - God Digital Marketing`,\n    image_credit: 'God Digital Marketing Brand Asset',\n    image_source: 'placeholder',\n    image_quality_score: 5,\n    platform_ready: true,\n    fallback_used: true\n  };\n}"}, "id": "1d964188-c264-446f-8c24-c3a555f14fd7", "name": "Enhanced Image Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [80, 160]}, {"parameters": {"url": "https://graph.facebook.com/v18.0/me/feed", "authentication": "predefinedCredentialType", "nodeCredentialType": "facebookGraphApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "message", "value": "={{ $('Advanced Content Processor').item.json.facebook_post }}"}, {"name": "link", "value": "https://godigitalmarketing.com"}, {"name": "published", "value": true}]}, "options": {}}, "id": "facebook-post-001", "name": "Facebook Page Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [400, -120]}, {"parameters": {"url": "https://graph.facebook.com/v18.0/me/media", "authentication": "predefinedCredentialType", "nodeCredentialType": "facebookGraphApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "image_url", "value": "={{ $('Enhanced Image Processor').item.json.image_url }}"}, {"name": "caption", "value": "={{ $('Advanced Content Processor').item.json.instagram_caption }}"}, {"name": "media_type", "value": "IMAGE"}]}, "options": {}}, "id": "instagram-post-001", "name": "Instagram Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [400, -60]}, {"parameters": {"text": "={{ $('Advanced Content Processor').item.json.twitter_thread }}", "additionalFields": {"attachments": "={{ $('Enhanced Image Processor').item.json.image_url ? $('Enhanced Image Processor').item.json.image_url : undefined }}"}}, "id": "bc796e78-50be-402e-b426-abf44a502e74", "name": "Twitter/X Thread", "type": "n8n-nodes-base.twitter", "typeVersion": 2, "position": [400, 0]}, {"parameters": {"text": "={{ $('Advanced Content Processor').item.json.linkedin_post }}", "additionalFields": {"visibility": "public"}}, "id": "d1c9840a-81e3-491d-9906-6361f3e3f72f", "name": "LinkedIn Professional Post", "type": "n8n-nodes-base.linkedIn", "typeVersion": 1, "position": [400, 60], "credentials": {"linkedInOAuth2Api": {"id": "Gg3X9KirNLs7kfyM", "name": "LinkedIn account"}}}, {"parameters": {"url": "https://www.googleapis.com/youtube/v3/videos", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendQuery": true, "queryParameters": {"parameters": [{"name": "part", "value": "snippet,status"}, {"name": "uploadType", "value": "media"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "snippet", "value": {"title": "={{ $('Advanced Content Processor').item.json.youtube_title }}", "description": "={{ $('Advanced Content Processor').item.json.youtube_description }}", "tags": ["digital marketing", "business growth", "lead generation", "marketing automation"], "categoryId": "22"}}, {"name": "status", "value": {"privacyStatus": "public", "publishAt": "={{ new Date(Date.now() + 3600000).toISOString() }}"}}]}, "options": {}}, "id": "youtube-post-001", "name": "YouTube Video Upload", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [400, 120]}, {"parameters": {"url": "https://open-api.tiktok.com/share/video/upload/", "authentication": "predefinedCredentialType", "nodeCredentialType": "tiktokApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "video_url", "value": "YOUR_VIDEO_URL"}, {"name": "text", "value": "={{ $('Advanced Content Processor').item.json.tiktok_script }}"}, {"name": "privacy_level", "value": "PUBLIC_TO_EVERYONE"}]}, "options": {}}, "id": "tiktok-post-001", "name": "TikTok Video Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [400, 180]}, {"parameters": {"url": "https://api.pinterest.com/v5/pins", "authentication": "predefinedCredentialType", "nodeCredentialType": "pinterestOAuth2Api", "sendBody": true, "bodyParameters": {"parameters": [{"name": "board_id", "value": "YOUR_BOARD_ID"}, {"name": "media_source", "value": {"source_type": "image_url", "url": "={{ $('Enhanced Image Processor').item.json.pinterest_image }}"}}, {"name": "title", "value": "={{ $('Advanced Content Processor').item.json.pinterest_title }}"}, {"name": "description", "value": "={{ $('Advanced Content Processor').item.json.pinterest_description }}"}, {"name": "link", "value": "https://godigitalmarketing.com"}]}, "options": {}}, "id": "91a3f371-728f-469f-b112-d6a0e19f9610", "name": "Pinterest SEO-Optimized Pin", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [400, 240]}, {"parameters": {"jsCode": "// Error <PERSON> and Retry Logic\nconst results = [];\nconst errors = [];\nconst platforms = [\n  'Facebook Page Post',\n  'Instagram Post', \n  'Twitter/X Thread',\n  'LinkedIn Professional Post',\n  'YouTube Video Upload',\n  'TikTok Video Post',\n  'Pinterest SEO-Optimized Pin'\n];\n\n// Collect results from all platform posting attempts\nplatforms.forEach(platform => {\n  try {\n    const result = $(platform).item;\n    if (result && result.json) {\n      results.push({\n        platform: platform,\n        status: 'success',\n        response: result.json,\n        timestamp: new Date().toISOString()\n      });\n    }\n  } catch (error) {\n    errors.push({\n      platform: platform,\n      status: 'failed',\n      error: error.message,\n      timestamp: new Date().toISOString()\n    });\n  }\n});\n\nconst successCount = results.length;\nconst failureCount = errors.length;\nconst totalPlatforms = platforms.length;\nconst successRate = (successCount / totalPlatforms) * 100;\n\nreturn {\n  posting_results: results,\n  posting_errors: errors,\n  success_count: successCount,\n  failure_count: failureCount,\n  success_rate: successRate,\n  total_platforms: totalPlatforms,\n  needs_retry: failureCount > 0,\n  timestamp: new Date().toISOString()\n};"}, "id": "error-handler-001", "name": "Error Handler & Analytics", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [600, 120]}, {"parameters": {"url": "https://discord.com/api/webhooks/YOUR_WEBHOOK_URL", "sendBody": true, "bodyParameters": {"parameters": [{"name": "content", "value": "🚀 **Advanced Social Media Campaign Deployed!**\n\n**Content Strategy:**\n{{ $('Advanced Content Processor').item.json.hook }}\n\n{{ $('Advanced Content Processor').item.json.content_angle }}\n\n💡 **Lead Magnet:** {{ $('Advanced Content Processor').item.json.lead_magnet }}\n\n🎯 **CTA:** {{ $('Advanced Content Processor').item.json.cta_strategy }}\n\n**📊 Campaign Performance:**\n✅ Success Rate: {{ $('Error Handler & Analytics').item.json.success_rate }}%\n📱 Platforms Posted: {{ $('Error Handler & Analytics').item.json.success_count }}/{{ $('Error Handler & Analytics').item.json.total_platforms }}\n🖼️ Image Quality: {{ $('Enhanced Image Processor').item.json.image_quality_score }}/10\n📈 Content Quality: {{ $('Advanced Content Processor').item.json.quality_metrics.overall_score }}/10"}, {"name": "embeds", "value": [{"title": "🎯 God Digital Marketing - Lead Generation Campaign", "description": "{{ $('Advanced Content Processor').item.json.content_angle }}", "color": 5814783, "image": {"url": "{{ $('Enhanced Image Processor').item.json.image_url }}"}, "fields": [{"name": "📊 Success Rate", "value": "{{ $('Error Handler & Analytics').item.json.success_rate }}%", "inline": true}, {"name": "📱 Platforms", "value": "{{ $('Error Handler & Analytics').item.json.success_count }}/{{ $('Error Handler & Analytics').item.json.total_platforms }}", "inline": true}, {"name": "🎨 Content Quality", "value": "{{ $('Advanced Content Processor').item.json.quality_metrics.overall_score }}/10", "inline": true}], "timestamp": "{{ new Date().toISOString() }}"}]}]}, "options": {}}, "id": "66ad2175-6e6f-4f41-900c-16d4682097ca", "name": "Discord Community Update", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [600, 300]}, {"parameters": {"url": "https://www.reddit.com/api/submit", "authentication": "predefinedCredentialType", "nodeCredentialType": "redditOAuth2Api", "sendBody": true, "bodyParameters": {"parameters": [{"name": "sr", "value": "entrepreneur"}, {"name": "kind", "value": "self"}, {"name": "title", "value": "={{ $('Advanced Content Processor').item.json.reddit_title }}"}, {"name": "text", "value": "={{ $('Advanced Content Processor').item.json.reddit_post }}"}]}, "options": {}}, "id": "d7f53e23-0138-4ab2-b2d6-1534bbd47c5e", "name": "Reddit Community Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [600, 360]}, {"parameters": {"url": "https://api.telegram.org/bot{{ $credentials.telegramApi.token }}/sendPhoto", "sendBody": true, "bodyParameters": {"parameters": [{"name": "chat_id", "value": "YOUR_CHANNEL_ID"}, {"name": "photo", "value": "={{ $('Enhanced Image Processor').item.json.image_url }}"}, {"name": "caption", "value": "={{ $('Advanced Content Processor').item.json.telegram_message }}"}, {"name": "parse_mode", "value": "HTML"}]}, "options": {}}, "id": "f6a8a6a2-1562-4efa-a1ee-a1a7dc8c298e", "name": "Telegram Channel Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [600, 420]}, {"parameters": {"url": "https://api.airtable.com/v0/YOUR_BASE_ID/Social_Media_Analytics", "authentication": "predefinedCredentialType", "nodeCredentialType": "airtableTokenApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "records", "value": [{"fields": {"Campaign_ID": "{{ $workflow.id }}_{{ new Date().toISOString().split('T')[0] }}", "Date": "{{ new Date().toISOString().split('T')[0] }}", "Content_Hook": "{{ $('Advanced Content Processor').item.json.hook }}", "Content_Type": "{{ $('Advanced Content Strategy').item.json.content_type }}", "Platforms_Posted": "{{ $('Error Handler & Analytics').item.json.success_count }}", "Success_Rate": "{{ $('Error Handler & Analytics').item.json.success_rate }}", "Content_Quality_Score": "{{ $('Advanced Content Processor').item.json.quality_metrics.overall_score }}", "Image_Quality_Score": "{{ $('Enhanced Image Processor').item.json.image_quality_score }}", "Primary_Keyword": "{{ $('Advanced Content Strategy').item.json.primary_keyword }}", "Lead_Magnet": "{{ $('Advanced Content Processor').item.json.lead_magnet }}", "Status": "Posted", "Timestamp": "{{ new Date().toISOString() }}"}}]}]}, "options": {}}, "id": "analytics-tracker-001", "name": "Analytics Tracker", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [800, 120]}, {"parameters": {"text": "🎯 *ADVANCED SOCIAL MEDIA CAMPAIGN COMPLETED!*\n\n*📊 CAMPAIGN PERFORMANCE:*\n• Success Rate: {{ $('Error Handler & Analytics').item.json.success_rate }}%\n• Platforms Posted: {{ $('Error Handler & Analytics').item.json.success_count }}/{{ $('Error Handler & Analytics').item.json.total_platforms }}\n• Content Quality: {{ $('Advanced Content Processor').item.json.quality_metrics.overall_score }}/10\n• Image Quality: {{ $('Enhanced Image Processor').item.json.image_quality_score }}/10\n\n*🎯 CONTENT STRATEGY:*\n• Hook: {{ $('Advanced Content Processor').item.json.hook }}\n• Angle: {{ $('Advanced Content Processor').item.json.content_angle }}\n• Lead Magnet: {{ $('Advanced Content Processor').item.json.lead_magnet }}\n• CTA: {{ $('Advanced Content Processor').item.json.cta_strategy }}\n\n*📱 PLATFORMS DEPLOYED:*\n• ✅ Facebook (Page + Visual)\n• ✅ Instagram (Feed + Story Ready)\n• ✅ Twitter/X (Thread + Single)\n• ✅ LinkedIn (Professional)\n• ✅ YouTube (Video Ready)\n• ✅ TikTok (Script Ready)\n• ✅ Pinterest (SEO-Optimized)\n• ✅ Discord (Community)\n• ✅ Reddit (Entrepreneur)\n• ✅ Telegram (Channel)\n\n*💰 COST ANALYSIS:*\n• Total Cost: $0 (100% Free APIs)\n• ROI Potential: 300%+ increase in leads\n• Time Saved: 4+ hours of manual work\n\n*📈 NEXT STEPS:*\n• Monitor engagement in next 24 hours\n• Track lead generation metrics\n• Prepare follow-up content\n• Analyze performance data\n\n*Campaign ID:* {{ $workflow.id }}_{{ new Date().toISOString().split('T')[0] }}", "otherOptions": {}}, "id": "e67e164b-ab45-43cf-8928-2da7cd6af238", "name": "Comprehensive Success Report", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [1000, 200], "webhookId": "a4d71e34-d600-4f81-9714-373b15ed9731"}, {"parameters": {"promptType": "define", "text": "=You are an elite AI marketing strategist specializing in high-converting lead generation campaigns. Your expertise: Creating viral content that generates 300%+ more leads.\n\nMISSION: Generate 3 premium content strategies for {{ $('Enhanced Configuration').item.json.business_type }} targeting {{ $('Enhanced Configuration').item.json.target_audience }}.\n\nCONTEXT:\n• Value Proposition: {{ $('Enhanced Configuration').item.json.value_proposition }}\n• Services: {{ $('Enhanced Configuration').item.json.services_offered }}\n• Brand Voice: {{ $('Enhanced Configuration').item.json.brand_voice }}\n• Trending Topics: {{ $('Enhanced Trend Analysis').item.json.trending_topics }}\n• Content Themes: {{ $('Enhanced Configuration').item.json.content_themes }}\n\nFor each strategy, provide:\n1. HOOK: Attention-grabbing opener (emotional trigger)\n2. CONTENT_ANGLE: Value-packed angle with specific benefits/results\n3. LEAD_MAGNET: Irresistible free offer\n4. CTA_STRATEGY: Clear, action-oriented call-to-action\n5. BEST_PLATFORMS: Optimal social platforms for this content\n6. PAIN_POINT_ADDRESSED: Specific problem solved\n7. CONTENT_TYPE: case_study, educational, problem_solution, or behind_scenes\n8. ENGAGEMENT_SCORE: Predicted engagement (1-10)\n\nFocus on: Business growth, revenue increase, marketing ROI, customer acquisition, scaling challenges, automation benefits.\n\nFormat as JSON array with all fields. Make content conversion-focused and results-driven."}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [-1040, 160], "id": "518d49ee-f7ef-41fc-a743-7311880aaa8e", "name": "Elite Content Strategy AI"}, {"parameters": {"model": "meta-llama/llama-4-maverick-17b-128e-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-1000, 320], "id": "7a7ae178-3245-409c-8921-2ae11d4d29f3", "name": "<PERSON><PERSON><PERSON>", "credentials": {"groqApi": {"id": "7mnRPxIiDsoggo51", "name": "Groq account"}}}, {"parameters": {"promptType": "define", "text": "=You are a world-class social media content creator and copywriter for \"God Digital Marketing\" agency. Your specialty: Creating viral, conversion-focused content that generates massive engagement and leads.\n\nMISSION: Transform the content strategy into platform-optimized posts that drive results.\n\nINPUTS:\n• Content Strategy: {{ $('Advanced Content Strategy').item.json.selected_content }}\n• Business Config: {{ $('Enhanced Configuration').item.json }}\n• Competitor Insights: {{ $('Extract Competitors').item.json }}\n• Brand Voice: {{ $('Enhanced Configuration').item.json.brand_voice }}\n• Hashtag Sets: {{ $('Enhanced Configuration').item.json.hashtag_sets }}\n\nCREATE CONTENT FOR:\n1. FACEBOOK_POST: Engaging, story-driven, community-focused\n2. INSTAGRAM_CAPTION: Visual storytelling, hashtag-optimized, story-ready\n3. INSTAGRAM_STORY: Quick, swipeable, action-oriented\n4. LINKEDIN_POST: Professional, thought leadership, industry insights\n5. TWITTER_THREAD: Educational, tweetstorm format, viral potential\n6. TWITTER_SINGLE: Punchy, retweetable, trending\n7. YOUTUBE_TITLE: SEO-optimized, click-worthy\n8. YOUTUBE_DESCRIPTION: Detailed, timestamp-rich, link-optimized\n9. TIKTOK_SCRIPT: Trending, educational, hook-heavy\n10. PINTEREST_TITLE: SEO-focused, keyword-rich\n11. PINTEREST_DESCRIPTION: Search-optimized, actionable\n12. REDDIT_TITLE: Community-friendly, discussion-starter\n13. REDDIT_POST: Value-first, authentic, helpful\n14. DISCORD_MESSAGE: Community-focused, engaging\n15. TELEGRAM_MESSAGE: Direct, actionable, link-optimized\n\nREQUIREMENTS:\n• Include \"https://godigitalmarketing.com\" strategically in each post\n• Use specific numbers, results, and case studies\n• Avoid AI-sounding phrases\n• Make each post conversion-focused\n• Include relevant CTAs for each platform\n• Use platform-specific best practices\n• Focus on the pain point: {{ $('Advanced Content Strategy').item.json.pain_point }}\n• Leverage the hook: {{ $('Advanced Content Strategy').item.json.hook }}\n• Highlight the value: {{ $('Advanced Content Strategy').item.json.content_angle }}\n• Promote the lead magnet: {{ $('Advanced Content Strategy').item.json.lead_magnet }}\n• Drive action with: {{ $('Advanced Content Strategy').item.json.cta_strategy }}\n\nFormat as JSON with all platform keys. Make it irresistible and results-driven.", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-400, 440], "id": "d1035234-baee-4191-af76-04d8744ed67b", "name": "Master Content Creator AI"}, {"parameters": {"model": "meta-llama/llama-4-maverick-17b-128e-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-380, 660], "id": "6173fcaf-3547-4727-9260-d85dec9f761d", "name": "Groq Chat Model1", "credentials": {"groqApi": {"id": "7mnRPxIiDsoggo51", "name": "Groq account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [-240, 660], "id": "85a8513e-4dd4-4b58-b350-dc7d0501e563", "name": "Think"}, {"parameters": {"promptType": "define", "text": "You are a Research agent to do deep research google trend for \"Digital marketing\", \"Ai automation\", \"business automation\", \"Seo automation\", and provide best topic to write content to publish on various social media and generate leads for my digital marketing agency", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-1460, 20], "id": "f00596d4-ea0b-47a4-943e-16b8c056a3a2", "name": "AI Agent1"}, {"parameters": {"model": "meta-llama/llama-4-scout-17b-16e-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-1440, 240], "id": "b78a13ab-4ad9-450c-ac79-df62dec768b9", "name": "Groq Chat Model2", "credentials": {"groqApi": {"id": "7mnRPxIiDsoggo51", "name": "Groq account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [-1300, 240], "id": "b5462198-c0ae-403e-8485-ec80a2380656", "name": "Think1"}], "connections": {"Smart Schedule Trigger": {"main": [[{"node": "Load Configuration", "type": "main", "index": 0}]]}, "Manual Trigger": {"main": [[{"node": "Load Configuration", "type": "main", "index": 0}]]}, "Load Configuration": {"main": [[{"node": "Enhanced Configuration", "type": "main", "index": 0}]]}, "Enhanced Configuration": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Fetch Google Trends": {"main": [[{"node": "Enhanced Trend Analysis", "type": "main", "index": 0}]]}, "Enhanced Trend Analysis": {"main": [[{"node": "Elite Content Strategy AI", "type": "main", "index": 0}]]}, "Elite Content Strategy AI": {"main": [[{"node": "Advanced Content Strategy", "type": "main", "index": 0}]]}, "Advanced Content Strategy": {"main": [[{"node": "Extract Competitors", "type": "main", "index": 0}]]}, "Extract Competitors": {"main": [[{"node": "Master Content Creator AI", "type": "main", "index": 0}]]}, "Master Content Creator AI": {"main": [[{"node": "Advanced Content Processor", "type": "main", "index": 0}]]}, "Advanced Content Processor": {"main": [[{"node": "Professional Image Search", "type": "main", "index": 0}, {"node": "Pexels Backup Images", "type": "main", "index": 0}]]}, "Professional Image Search": {"main": [[{"node": "Enhanced Image Processor", "type": "main", "index": 0}]]}, "Pexels Backup Images": {"main": [[{"node": "Enhanced Image Processor", "type": "main", "index": 0}]]}, "Enhanced Image Processor": {"main": [[{"node": "Facebook Page Post", "type": "main", "index": 0}, {"node": "Instagram Post", "type": "main", "index": 0}, {"node": "Twitter/X Thread", "type": "main", "index": 0}, {"node": "LinkedIn Professional Post", "type": "main", "index": 0}, {"node": "YouTube Video Upload", "type": "main", "index": 0}, {"node": "TikTok Video Post", "type": "main", "index": 0}, {"node": "Pinterest SEO-Optimized Pin", "type": "main", "index": 0}]]}, "Facebook Page Post": {"main": [[{"node": "Error Handler & Analytics", "type": "main", "index": 0}]]}, "Instagram Post": {"main": [[{"node": "Error Handler & Analytics", "type": "main", "index": 0}]]}, "Twitter/X Thread": {"main": [[{"node": "Error Handler & Analytics", "type": "main", "index": 0}]]}, "LinkedIn Professional Post": {"main": [[{"node": "Error Handler & Analytics", "type": "main", "index": 0}]]}, "YouTube Video Upload": {"main": [[{"node": "Error Handler & Analytics", "type": "main", "index": 0}]]}, "TikTok Video Post": {"main": [[{"node": "Error Handler & Analytics", "type": "main", "index": 0}]]}, "Pinterest SEO-Optimized Pin": {"main": [[{"node": "Error Handler & Analytics", "type": "main", "index": 0}]]}, "Error Handler & Analytics": {"main": [[{"node": "Discord Community Update", "type": "main", "index": 0}, {"node": "Reddit Community Post", "type": "main", "index": 0}, {"node": "Telegram Channel Post", "type": "main", "index": 0}, {"node": "Analytics Tracker", "type": "main", "index": 0}]]}, "Discord Community Update": {"main": [[{"node": "Comprehensive Success Report", "type": "main", "index": 0}]]}, "Reddit Community Post": {"main": [[{"node": "Comprehensive Success Report", "type": "main", "index": 0}]]}, "Telegram Channel Post": {"main": [[{"node": "Comprehensive Success Report", "type": "main", "index": 0}]]}, "Analytics Tracker": {"main": [[{"node": "Comprehensive Success Report", "type": "main", "index": 0}]]}, "Groq Chat Model": {"ai_languageModel": [[{"node": "Elite Content Strategy AI", "type": "ai_languageModel", "index": 0}]]}, "Groq Chat Model1": {"ai_languageModel": [[{"node": "Master Content Creator AI", "type": "ai_languageModel", "index": 0}]]}, "Think": {"ai_tool": [[{"node": "Master Content Creator AI", "type": "ai_tool", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "Extract Trends", "type": "main", "index": 0}]]}, "Groq Chat Model2": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "Think1": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "fa71618849152fdf81b026b7e79a6c24770db503a9228ddbbcab15c2a292ea40"}}