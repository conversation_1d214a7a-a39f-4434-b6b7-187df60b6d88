# Advanced Organic Traffic Generation System Configuration

# Target Website Configuration
target:
  url: "https://example.com"  # Replace with your target website
  domain: "example.com"       # Domain for link detection
  
# Keywords Configuration
keywords:
  # High-priority keywords (more traffic allocation)
  primary:
    - "digital marketing services"
    - "SEO optimization"
    - "web development company"
  
  # Medium-priority keywords
  secondary:
    - "online marketing"
    - "website design"
    - "social media marketing"
  
  # Long-tail keywords (lower traffic, higher conversion)
  longtail:
    - "best digital marketing agency near me"
    - "affordable web development services"
    - "professional SEO consultant"

# Traffic Volume Configuration
traffic:
  daily_volume: 1000          # Total daily impressions target
  clicks_per_keyword:
    min: 10                   # Minimum clicks per keyword
    max: 40                   # Maximum clicks per keyword
  
  # Traffic distribution by keyword priority
  distribution:
    primary: 0.5              # 50% of traffic to primary keywords
    secondary: 0.3            # 30% to secondary keywords
    longtail: 0.2             # 20% to longtail keywords

# Geographic Targeting
regions:
  primary:
    - "US"                    # United States
    - "CA"                    # Canada
    - "GB"                    # United Kingdom
    - "AU"                    # Australia
  
  secondary:
    - "DE"                    # Germany
    - "FR"                    # France
    - "NL"                    # Netherlands
    - "SE"                    # Sweden

# Proxy Configuration
proxy:
  provider: "mobile_proxy_service"  # Your proxy provider
  api_key: "${PROXY_API_KEY}"       # Set in environment variables
  rotation_interval: 3600           # Rotate proxies every hour
  max_failures: 3                   # Max failures before proxy rotation
  
  # Proxy types preference
  types:
    - "mobile"                      # Mobile proxies (preferred)
    - "residential"                 # Residential proxies
    - "datacenter"                  # Datacenter proxies (fallback)

# Browser Fingerprinting
fingerprinting:
  # Device distribution
  device_distribution:
    mobile: 0.6               # 60% mobile traffic
    desktop: 0.4              # 40% desktop traffic
  
  # Browser distribution
  browser_distribution:
    chrome: 0.7               # 70% Chrome
    firefox: 0.15             # 15% Firefox
    safari: 0.1               # 10% Safari
    edge: 0.05                # 5% Edge
  
  # Operating system distribution
  os_distribution:
    windows: 0.4              # 40% Windows
    android: 0.35             # 35% Android
    ios: 0.15                 # 15% iOS
    macos: 0.1                # 10% macOS

# Behavior Simulation
behavior:
  # Search behavior
  search_delays:
    typing_speed: [0.05, 0.3] # Typing delay range (seconds)
    search_wait: [1, 3]       # Wait before search (seconds)
  
  # Page interaction
  scrolling:
    speed: [100, 500]         # Scroll speed (pixels/second)
    pauses: [0.5, 2.0]        # Pause duration range
    depth: [0.3, 0.8]         # Scroll depth (percentage)
  
  # Reading simulation
  reading:
    min_time: 180             # Minimum reading time (seconds)
    max_time: 240             # Maximum reading time (seconds)
    pages_to_visit: [2, 3]    # Additional pages to visit
  
  # Mouse movements
  mouse:
    enable_movements: true    # Enable realistic mouse movements
    movement_speed: [1, 3]    # Movement speed range
    random_clicks: true       # Random non-functional clicks

# Scheduling Configuration
scheduling:
  # Operating hours (24-hour format)
  operating_hours:
    start: 8                  # Start at 8 AM
    end: 22                   # End at 10 PM
  
  # Session distribution
  sessions_per_day: [8, 12]   # Number of session batches per day
  batch_size_variance: 0.3    # Variance in batch sizes (±30%)
  
  # Rate limiting
  rate_limiting:
    max_concurrent: 5         # Max concurrent sessions
    delay_between_sessions: [30, 120]  # Delay between sessions (seconds)

# Analytics & Logging
analytics:
  log_level: "INFO"           # DEBUG, INFO, WARNING, ERROR
  log_file: "logs/traffic_generation.log"
  
  # Metrics to track
  metrics:
    - "successful_visits"
    - "failed_visits"
    - "average_session_duration"
    - "pages_per_session"
    - "bounce_rate"
    - "keyword_performance"
  
  # Reporting
  reports:
    daily_summary: true       # Generate daily reports
    keyword_analysis: true    # Keyword performance analysis
    export_format: "json"     # json, csv, xlsx

# Error Handling
error_handling:
  max_retries: 3              # Max retries per session
  retry_delay: [5, 15]        # Delay between retries (seconds)
  
  # Failure thresholds
  thresholds:
    proxy_failure_rate: 0.3   # Switch proxy if >30% failures
    keyword_failure_rate: 0.5 # Skip keyword if >50% failures
  
  # Recovery strategies
  recovery:
    auto_proxy_rotation: true
    fallback_search_engines: ["bing.com", "duckduckgo.com"]
    emergency_stop_threshold: 0.8  # Stop if >80% overall failure rate

# Compliance & Safety
compliance:
  respect_robots_txt: true    # Check and respect robots.txt
  user_agent_rotation: true  # Rotate user agents
  request_rate_limit: 2       # Max requests per second
  
  # Safety measures
  safety:
    max_daily_requests: 5000  # Hard limit on daily requests
    cooldown_period: 300      # Cooldown after errors (seconds)
    blacklist_check: true     # Check against known blacklists
