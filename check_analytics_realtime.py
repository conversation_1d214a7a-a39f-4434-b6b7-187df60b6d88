#!/usr/bin/env python3
"""
Real-time Google Analytics checker
Helps verify if traffic is appearing in Analytics
"""

import asyncio
import aiohttp
import time
from datetime import datetime

class AnalyticsChecker:
    def __init__(self):
        self.check_count = 0
        
    async def simulate_analytics_check(self):
        """Simulate checking Google Analytics Real-time"""
        print("🔍 SIMULATING GOOGLE ANALYTICS REAL-TIME CHECK")
        print("=" * 50)
        print("📊 What you should see in Google Analytics:")
        print("=" * 50)
        
        # Simulate what should appear in Analytics
        print("1. 📊 Go to Google Analytics")
        print("2. 🔍 Click 'Realtime' in left menu")
        print("3. 📈 Click 'Overview'")
        print("4. 👀 Look for:")
        print("   - Active users: 1-3 users")
        print("   - Page views: /")
        print("   - Page views: /tours")
        print("   - Page views: /contact")
        print("   - Traffic source: Google / organic")
        print("   - Location: Your country")
        
        print("\n⏰ TIMING EXPECTATIONS:")
        print("- Real-time data: 1-5 minutes delay")
        print("- Standard reports: 24-48 hours")
        print("- Search Console: 1-3 days")
        
    async def verify_website_accessibility(self):
        """Verify Balkland.com is accessible and has Analytics"""
        print("\n🔍 VERIFYING WEBSITE ACCESSIBILITY")
        print("=" * 40)
        
        target_url = "https://balkland.com"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(target_url, headers=headers) as response:
                    if response.status == 200:
                        content = await response.text()
                        
                        print(f"✅ Website accessible: {target_url}")
                        print(f"📊 Response status: {response.status}")
                        
                        # Check for Analytics tracking
                        analytics_found = False
                        if 'gtag' in content:
                            print("✅ Google Analytics (gtag) detected")
                            analytics_found = True
                        if 'google-analytics' in content:
                            print("✅ Google Analytics (classic) detected")
                            analytics_found = True
                        if 'GA_MEASUREMENT_ID' in content:
                            print("✅ Google Analytics (GA4) detected")
                            analytics_found = True
                        if 'googletagmanager' in content:
                            print("✅ Google Tag Manager detected")
                            analytics_found = True
                            
                        if not analytics_found:
                            print("⚠️ No Google Analytics tracking code found")
                            print("💡 This might be why traffic isn't showing")
                            
                        return True
                    else:
                        print(f"❌ Website not accessible: {response.status}")
                        return False
                        
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False
    
    async def generate_test_pageview(self):
        """Generate a test pageview to verify tracking"""
        print("\n🧪 GENERATING TEST PAGEVIEW")
        print("=" * 30)
        
        target_url = "https://balkland.com"
        
        # Ultra-realistic headers
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://www.google.com/search?q=balkland+tours',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive'
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                print(f"🔍 Visiting: {target_url}")
                print(f"📊 Referrer: Google search")
                
                async with session.get(target_url, headers=headers) as response:
                    if response.status == 200:
                        print("✅ Page loaded successfully")
                        print("⏱️ Staying on page for 30 seconds...")
                        await asyncio.sleep(30)
                        print("✅ Test pageview completed")
                        print("📊 This should appear in Analytics Real-time")
                        return True
                    else:
                        print(f"❌ Page load failed: {response.status}")
                        return False
                        
        except Exception as e:
            print(f"❌ Test pageview error: {e}")
            return False
    
    async def run_analytics_verification(self):
        """Run complete analytics verification"""
        print("🔍 GOOGLE ANALYTICS VERIFICATION SYSTEM")
        print("=" * 50)
        print(f"⏰ Check time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        # Step 1: Check website accessibility
        website_ok = await self.verify_website_accessibility()
        
        if website_ok:
            # Step 2: Generate test pageview
            await self.generate_test_pageview()
            
            # Step 3: Provide Analytics checking instructions
            await self.simulate_analytics_check()
            
            print("\n" + "=" * 50)
            print("🎯 NEXT STEPS:")
            print("=" * 50)
            print("1. 🔍 Open Google Analytics in your browser")
            print("2. 📊 Go to Realtime → Overview")
            print("3. ⏰ Wait 5-10 minutes for data to appear")
            print("4. 👀 Look for the test pageview we just generated")
            print("5. 🔄 Refresh the page if needed")
            
            print("\n💡 TROUBLESHOOTING:")
            print("- If no traffic appears, check Analytics tracking code")
            print("- Verify the correct Analytics property is selected")
            print("- Check if ad blockers are interfering")
            print("- Ensure Analytics is properly configured")
            
        else:
            print("\n❌ WEBSITE ACCESSIBILITY ISSUE")
            print("Cannot proceed with Analytics verification")
            print("Please check website connectivity")

if __name__ == "__main__":
    checker = AnalyticsChecker()
    asyncio.run(checker.run_analytics_verification())
