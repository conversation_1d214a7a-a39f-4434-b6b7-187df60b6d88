#!/usr/bin/env python3
"""
Real-time traffic verification script to check if traffic is reaching Balkland.com
and being tracked by Google Analytics
"""

import asyncio
import aiohttp
import random
import time
from datetime import datetime

class AnalyticsTrafficChecker:
    def __init__(self):
        self.test_results = []
        
    async def test_google_analytics_tracking(self):
        """Test if traffic is properly tracked by Google Analytics"""
        print("🔍 TESTING GOOGLE ANALYTICS TRACKING...")
        print("=" * 60)
        
        # Test with proper Google referrer
        search_keyword = "Balkland tours Serbia"
        search_url = f"https://www.google.com/search?q={search_keyword.replace(' ', '+')}"
        target_url = "https://balkland.com"
        
        # Real browser headers for proper tracking
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': search_url,
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Dest': 'document',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive'
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                print(f"🔍 Testing: {search_keyword} -> {target_url}")
                print(f"📊 Google Referrer: {search_url}")
                
                # Visit Balkland.com with Google referrer
                async with session.get(target_url, headers=headers) as response:
                    status = response.status
                    content = await response.text()
                    
                    if status == 200:
                        print(f"✅ SUCCESS: {target_url} responded with 200")
                        
                        # Check for Google Analytics tracking code
                        if 'gtag' in content or 'google-analytics' in content or 'GA_MEASUREMENT_ID' in content:
                            print("✅ GOOGLE ANALYTICS: Tracking code detected")
                        else:
                            print("⚠️ GOOGLE ANALYTICS: No tracking code found")
                            
                        # Check for Google Tag Manager
                        if 'googletagmanager' in content or 'GTM-' in content:
                            print("✅ GOOGLE TAG MANAGER: Detected")
                        else:
                            print("⚠️ GOOGLE TAG MANAGER: Not detected")
                            
                        # Simulate realistic browsing
                        print("🔄 Simulating realistic browsing behavior...")
                        await asyncio.sleep(random.uniform(30, 60))  # Stay on page 30-60 seconds
                        
                        # Visit additional pages
                        additional_pages = [
                            "https://balkland.com/tours",
                            "https://balkland.com/about",
                            "https://balkland.com/contact"
                        ]
                        
                        for page in additional_pages[:2]:  # Visit 2 more pages
                            try:
                                page_headers = headers.copy()
                                page_headers['Referer'] = target_url
                                
                                async with session.get(page, headers=page_headers) as page_response:
                                    if page_response.status == 200:
                                        print(f"✅ ADDITIONAL PAGE: {page} - 200 OK")
                                        await asyncio.sleep(random.uniform(20, 40))
                                    else:
                                        print(f"⚠️ ADDITIONAL PAGE: {page} - {page_response.status}")
                            except Exception as e:
                                print(f"⚠️ ADDITIONAL PAGE ERROR: {page} - {e}")
                        
                        print("✅ REALISTIC BROWSING: Completed 3 pages, 90+ seconds")
                        return True
                        
                    else:
                        print(f"❌ FAILED: {target_url} responded with {status}")
                        return False
                        
        except Exception as e:
            print(f"❌ ERROR: {e}")
            return False
    
    async def test_multiple_traffic_sources(self):
        """Test traffic from multiple sources"""
        print("\n🌐 TESTING MULTIPLE TRAFFIC SOURCES...")
        print("=" * 60)
        
        traffic_sources = [
            {
                'name': 'Google Search',
                'referrer': 'https://www.google.com/search?q=balkland+tours',
                'target': 'https://balkland.com'
            },
            {
                'name': 'Facebook',
                'referrer': 'https://www.facebook.com/',
                'target': 'https://balkland.com'
            },
            {
                'name': 'Direct Traffic',
                'referrer': '',
                'target': 'https://balkland.com'
            }
        ]
        
        for source in traffic_sources:
            print(f"\n📊 Testing {source['name']} traffic...")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Connection': 'keep-alive'
            }
            
            if source['referrer']:
                headers['Referer'] = source['referrer']
                headers['Sec-Fetch-Site'] = 'cross-site'
            else:
                headers['Sec-Fetch-Site'] = 'none'
                
            headers['Sec-Fetch-Mode'] = 'navigate'
            headers['Sec-Fetch-Dest'] = 'document'
            
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(source['target'], headers=headers) as response:
                        if response.status == 200:
                            print(f"✅ {source['name']}: SUCCESS - 200 OK")
                            await asyncio.sleep(random.uniform(15, 30))
                        else:
                            print(f"❌ {source['name']}: FAILED - {response.status}")
            except Exception as e:
                print(f"❌ {source['name']}: ERROR - {e}")
    
    async def run_comprehensive_test(self):
        """Run comprehensive traffic test"""
        print("🧪 COMPREHENSIVE GOOGLE ANALYTICS TRAFFIC TEST")
        print("=" * 60)
        print(f"⏰ Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # Test 1: Google Analytics tracking
        test1_result = await self.test_google_analytics_tracking()
        
        # Test 2: Multiple traffic sources
        await self.test_multiple_traffic_sources()
        
        print("\n" + "=" * 60)
        print("📊 TRAFFIC TEST SUMMARY")
        print("=" * 60)
        
        if test1_result:
            print("✅ GOOGLE ANALYTICS: Traffic properly sent with referrer")
            print("✅ REALISTIC BEHAVIOR: 90+ seconds, 3 pages visited")
            print("✅ PROPER HEADERS: All tracking headers included")
        else:
            print("❌ GOOGLE ANALYTICS: Issues detected")
            
        print("\n💡 IMPORTANT NOTES:")
        print("1. Google Analytics has 24-48 hour delay for data processing")
        print("2. Check GA Real-time reports first (Realtime > Overview)")
        print("3. Google Search Console can take 1-3 days to show data")
        print("4. Ensure GA tracking code is properly installed on Balkland.com")
        print("5. Check if ad blockers or privacy settings are blocking tracking")
        
        print("\n🔍 NEXT STEPS:")
        print("1. Check Google Analytics Real-time reports in 5-10 minutes")
        print("2. Verify GA tracking code installation")
        print("3. Check Google Search Console in 24-48 hours")
        print("4. Monitor traffic patterns over next few days")

if __name__ == "__main__":
    checker = AnalyticsTrafficChecker()
    asyncio.run(checker.run_comprehensive_test())
