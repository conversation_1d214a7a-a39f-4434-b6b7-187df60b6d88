"""
Advanced Proxy Management System

This module provides sophisticated proxy rotation with mobile proxy support,
IP diversity management, geographic targeting, and comprehensive health monitoring.
"""

import asyncio
import random
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from pathlib import Path
import aiohttp
import requests
from loguru import logger
from config_manager import config

@dataclass
class ProxyInfo:
    """Data class for proxy information"""
    host: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    protocol: str = "http"
    region: str = "US"
    proxy_type: str = "datacenter"  # mobile, residential, datacenter
    provider: str = "unknown"
    last_used: Optional[datetime] = None
    failure_count: int = 0
    success_count: int = 0
    response_time: float = 0.0
    is_active: bool = True
    health_score: float = 1.0

    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        total = self.success_count + self.failure_count
        return self.success_count / total if total > 0 else 1.0

    @property
    def proxy_url(self) -> str:
        """Get formatted proxy URL"""
        if self.username and self.password:
            return f"{self.protocol}://{self.username}:{self.password}@{self.host}:{self.port}"
        return f"{self.protocol}://{self.host}:{self.port}"

class ProxyManager:
    """Advanced proxy management with health monitoring and intelligent rotation"""

    def __init__(self):
        """Initialize proxy manager with configuration"""
        self.proxy_config = config.proxy
        self.regions_config = config.regions

        # Proxy pools organized by type and region
        self.proxy_pools: Dict[str, Dict[str, List[ProxyInfo]]] = {
            'mobile': {},
            'residential': {},
            'datacenter': {}
        }

        # Health monitoring
        self.health_check_interval = 300  # 5 minutes
        self.last_health_check = None
        self.blacklisted_proxies: set = set()

        # Usage tracking
        self.usage_stats: Dict[str, Dict] = {}
        self.rotation_strategy = "round_robin"  # round_robin, random, health_based
        self.current_indices: Dict[str, int] = {}

        # Initialize proxy pools
        self._initialize_proxy_pools()

        logger.info("Advanced proxy manager initialized")

    def _initialize_proxy_pools(self):
        """Initialize proxy pools for different types and regions"""
        for proxy_type in ['mobile', 'residential', 'datacenter']:
            self.proxy_pools[proxy_type] = {}
            for region_group in ['primary', 'secondary']:
                regions = self.regions_config.get(region_group, [])
                for region in regions:
                    self.proxy_pools[proxy_type][region] = []
                    self.current_indices[f"{proxy_type}_{region}"] = 0

    async def refresh_proxies(self, force: bool = False) -> bool:
        """Refresh proxy lists from providers"""
        try:
            if not force and not self._should_refresh():
                return True

            logger.info("Refreshing proxy lists from providers...")

            # Refresh from primary provider
            success = await self._fetch_proxies_from_provider()

            if not success:
                logger.warning("Primary provider failed, trying backup sources")
                success = await self._fetch_backup_proxies()

            if success:
                # Perform health checks on new proxies
                await self._perform_health_checks()
                logger.info("Proxy refresh completed successfully")
            else:
                logger.error("All proxy sources failed")

            return success

        except Exception as e:
            logger.error(f"Error refreshing proxies: {e}")
            return False

    def _should_refresh(self) -> bool:
        """Check if proxies should be refreshed"""
        if not self.last_health_check:
            return True

        time_since_refresh = datetime.now() - self.last_health_check
        return time_since_refresh > timedelta(seconds=self.proxy_config.rotation_interval)

    async def _fetch_proxies_from_provider(self) -> bool:
        """Fetch proxies from primary provider API"""
        try:
            api_url = "https://api.proxy-provider.com/v1/proxies"  # Replace with actual API

            async with aiohttp.ClientSession() as session:
                for proxy_type in self.proxy_config.types:
                    for region_group in ['primary', 'secondary']:
                        regions = self.regions_config.get(region_group, [])

                        for region in regions:
                            params = {
                                "apiKey": self.proxy_config.api_key,
                                "type": proxy_type,
                                "country": region,
                                "limit": 50,
                                "format": "json"
                            }

                            try:
                                async with session.get(api_url, params=params) as response:
                                    if response.status == 200:
                                        data = await response.json()
                                        proxies = self._parse_proxy_response(data, proxy_type, region)
                                        self.proxy_pools[proxy_type][region] = proxies
                                        logger.debug(f"Fetched {len(proxies)} {proxy_type} proxies for {region}")
                                    else:
                                        logger.warning(f"Failed to fetch {proxy_type} proxies for {region}: {response.status}")

                            except Exception as e:
                                logger.error(f"Error fetching {proxy_type} proxies for {region}: {e}")
                                continue

                            # Rate limiting
                            await asyncio.sleep(0.5)

            self.last_health_check = datetime.now()
            return True

        except Exception as e:
            logger.error(f"Error in primary proxy fetch: {e}")
            return False

    def _parse_proxy_response(self, data: Dict, proxy_type: str, region: str) -> List[ProxyInfo]:
        """Parse proxy provider response into ProxyInfo objects"""
        proxies = []

        try:
            proxy_list = data.get('proxies', data.get('data', []))

            for proxy_data in proxy_list:
                proxy = ProxyInfo(
                    host=proxy_data.get('ip', proxy_data.get('host')),
                    port=int(proxy_data.get('port', 8080)),
                    username=proxy_data.get('username'),
                    password=proxy_data.get('password'),
                    protocol=proxy_data.get('protocol', 'http'),
                    region=region,
                    proxy_type=proxy_type,
                    provider=proxy_data.get('provider', 'primary')
                )

                if proxy.host:  # Ensure we have a valid host
                    proxies.append(proxy)

        except Exception as e:
            logger.error(f"Error parsing proxy response: {e}")

        return proxies

    async def _fetch_backup_proxies(self) -> bool:
        """Fetch proxies from backup sources"""
        try:
            # Load backup proxies from file if available
            backup_file = Path("backup_proxies.json")
            if backup_file.exists():
                with open(backup_file, 'r') as f:
                    backup_data = json.load(f)

                for proxy_type, regions in backup_data.items():
                    if proxy_type in self.proxy_pools:
                        for region, proxy_list in regions.items():
                            if region in self.proxy_pools[proxy_type]:
                                proxies = [ProxyInfo(**proxy_data) for proxy_data in proxy_list]
                                self.proxy_pools[proxy_type][region].extend(proxies)

                logger.info("Loaded backup proxies")
                return True

            # Fallback to hardcoded proxies (for development/testing)
            fallback_proxies = self._get_fallback_proxies()
            if fallback_proxies:
                for proxy_type, regions in fallback_proxies.items():
                    for region, proxies in regions.items():
                        if proxy_type in self.proxy_pools and region in self.proxy_pools[proxy_type]:
                            self.proxy_pools[proxy_type][region] = proxies

                logger.warning("Using fallback proxies")
                return True

            return False

        except Exception as e:
            logger.error(f"Error loading backup proxies: {e}")
            return False

    def _get_fallback_proxies(self) -> Dict[str, Dict[str, List[ProxyInfo]]]:
        """Get fallback proxies for development/testing"""
        return {
            'datacenter': {
                'US': [
                    ProxyInfo(host="proxy1.example.com", port=8080, region="US", proxy_type="datacenter"),
                    ProxyInfo(host="proxy2.example.com", port=8080, region="US", proxy_type="datacenter")
                ],
                'UK': [
                    ProxyInfo(host="proxy3.example.com", port=8080, region="UK", proxy_type="datacenter")
                ]
            }
        }

    async def get_proxy(self, region: str = None, proxy_type: str = None,
                       exclude_failed: bool = True) -> Optional[ProxyInfo]:
        """Get next available proxy with intelligent selection"""
        try:
            # Refresh proxies if needed
            await self.refresh_proxies()

            # Determine proxy type preference
            if not proxy_type:
                proxy_type = self._select_proxy_type()

            # Determine region preference
            if not region:
                region = self._select_region()

            # Get proxy from pool
            proxy = self._get_proxy_from_pool(proxy_type, region, exclude_failed)

            if not proxy:
                # Try other regions/types if primary choice failed
                proxy = self._get_fallback_proxy(exclude_failed)

            if proxy:
                # Update usage tracking
                self._update_proxy_usage(proxy)
                logger.debug(f"Selected {proxy.proxy_type} proxy from {proxy.region}: {proxy.host}")
            else:
                logger.warning("No available proxies found")

            return proxy

        except Exception as e:
            logger.error(f"Error getting proxy: {e}")
            return None

    def _select_proxy_type(self) -> str:
        """Select proxy type based on configuration and availability"""
        # Prefer mobile proxies, then residential, then datacenter
        for proxy_type in self.proxy_config.types:
            if self._has_available_proxies(proxy_type):
                return proxy_type

        # Fallback to any available type
        for proxy_type in ['mobile', 'residential', 'datacenter']:
            if self._has_available_proxies(proxy_type):
                return proxy_type

        return 'datacenter'  # Default fallback

    def _select_region(self) -> str:
        """Select region based on configuration and availability"""
        # Prefer primary regions
        for region in self.regions_config.get('primary', []):
            if self._has_available_proxies_in_region(region):
                return region

        # Fallback to secondary regions
        for region in self.regions_config.get('secondary', []):
            if self._has_available_proxies_in_region(region):
                return region

        return 'US'  # Default fallback

    def _has_available_proxies(self, proxy_type: str) -> bool:
        """Check if proxy type has available proxies"""
        if proxy_type not in self.proxy_pools:
            return False

        for region_proxies in self.proxy_pools[proxy_type].values():
            if any(p.is_active and p.health_score > 0.3 for p in region_proxies):
                return True

        return False

    def _has_available_proxies_in_region(self, region: str) -> bool:
        """Check if region has available proxies"""
        for proxy_type in self.proxy_pools:
            if region in self.proxy_pools[proxy_type]:
                proxies = self.proxy_pools[proxy_type][region]
                if any(p.is_active and p.health_score > 0.3 for p in proxies):
                    return True
        return False

    def _get_proxy_from_pool(self, proxy_type: str, region: str,
                           exclude_failed: bool) -> Optional[ProxyInfo]:
        """Get proxy from specific pool using rotation strategy"""
        if proxy_type not in self.proxy_pools or region not in self.proxy_pools[proxy_type]:
            return None

        proxies = self.proxy_pools[proxy_type][region]
        if not proxies:
            return None

        # Filter out failed proxies if requested
        if exclude_failed:
            available_proxies = [
                p for p in proxies
                if p.is_active and p.health_score > 0.3 and p.failure_count < self.proxy_config.max_failures
            ]
        else:
            available_proxies = [p for p in proxies if p.is_active]

        if not available_proxies:
            return None

        # Apply rotation strategy
        if self.rotation_strategy == "random":
            return random.choice(available_proxies)

        elif self.rotation_strategy == "health_based":
            # Select based on health score and success rate
            weights = [p.health_score * p.success_rate for p in available_proxies]
            return random.choices(available_proxies, weights=weights)[0]

        else:  # round_robin
            pool_key = f"{proxy_type}_{region}"
            index = self.current_indices.get(pool_key, 0)

            # Find next available proxy in round-robin fashion
            for i in range(len(available_proxies)):
                proxy_index = (index + i) % len(available_proxies)
                proxy = available_proxies[proxy_index]

                # Update index for next call
                self.current_indices[pool_key] = (proxy_index + 1) % len(available_proxies)
                return proxy

        return None

    def _get_fallback_proxy(self, exclude_failed: bool) -> Optional[ProxyInfo]:
        """Get any available proxy as fallback"""
        for proxy_type in ['mobile', 'residential', 'datacenter']:
            for region in ['US', 'UK', 'CA', 'AU', 'DE', 'FR']:
                proxy = self._get_proxy_from_pool(proxy_type, region, exclude_failed)
                if proxy:
                    return proxy
        return None

    def _update_proxy_usage(self, proxy: ProxyInfo):
        """Update proxy usage statistics"""
        proxy.last_used = datetime.now()

        # Update global usage stats
        key = f"{proxy.proxy_type}_{proxy.region}"
        if key not in self.usage_stats:
            self.usage_stats[key] = {
                'total_uses': 0,
                'last_used': None,
                'average_response_time': 0.0
            }

        self.usage_stats[key]['total_uses'] += 1
        self.usage_stats[key]['last_used'] = datetime.now()

    async def _perform_health_checks(self):
        """Perform health checks on all proxies"""
        logger.info("Starting proxy health checks...")

        health_check_tasks = []

        for proxy_type in self.proxy_pools:
            for region in self.proxy_pools[proxy_type]:
                for proxy in self.proxy_pools[proxy_type][region]:
                    if proxy.is_active:
                        task = asyncio.create_task(self._check_proxy_health(proxy))
                        health_check_tasks.append(task)

        if health_check_tasks:
            await asyncio.gather(*health_check_tasks, return_exceptions=True)

        # Update health scores and deactivate failed proxies
        self._update_proxy_health_scores()

        logger.info("Proxy health checks completed")

    async def _check_proxy_health(self, proxy: ProxyInfo) -> bool:
        """Check individual proxy health"""
        try:
            start_time = time.time()
            timeout = aiohttp.ClientTimeout(total=10)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                try:
                    async with session.get(
                        'http://httpbin.org/ip',
                        proxy=proxy.proxy_url
                    ) as response:
                        if response.status == 200:
                            response_time = time.time() - start_time
                            proxy.response_time = response_time
                            proxy.success_count += 1
                            return True
                        else:
                            proxy.failure_count += 1
                            return False

                except Exception:
                    proxy.failure_count += 1
                    return False

        except Exception as e:
            logger.debug(f"Health check failed for {proxy.host}: {e}")
            proxy.failure_count += 1
            return False

    def _update_proxy_health_scores(self):
        """Update health scores for all proxies"""
        for proxy_type in self.proxy_pools:
            for region in self.proxy_pools[proxy_type]:
                for proxy in self.proxy_pools[proxy_type][region]:
                    # Calculate health score based on success rate and response time
                    success_rate = proxy.success_rate

                    # Response time factor (lower is better)
                    time_factor = max(0.1, 1.0 - (proxy.response_time / 10.0))

                    # Failure count penalty
                    failure_penalty = max(0.1, 1.0 - (proxy.failure_count / 10.0))

                    # Combined health score
                    proxy.health_score = success_rate * time_factor * failure_penalty

                    # Deactivate proxies with too many failures
                    if proxy.failure_count >= self.proxy_config.max_failures:
                        proxy.is_active = False
                        logger.warning(f"Deactivated proxy {proxy.host} due to failures")

    def report_proxy_success(self, proxy: ProxyInfo):
        """Report successful proxy usage"""
        proxy.success_count += 1
        proxy.health_score = min(1.0, proxy.health_score + 0.1)
        logger.debug(f"Proxy success reported: {proxy.host}")

    def report_proxy_failure(self, proxy: ProxyInfo, error: str = None):
        """Report proxy failure"""
        proxy.failure_count += 1
        proxy.health_score = max(0.1, proxy.health_score - 0.2)

        if proxy.failure_count >= self.proxy_config.max_failures:
            proxy.is_active = False
            self.blacklisted_proxies.add(f"{proxy.host}:{proxy.port}")

        logger.warning(f"Proxy failure reported: {proxy.host} - {error}")

    def get_proxy_statistics(self) -> Dict[str, Any]:
        """Get comprehensive proxy statistics"""
        stats = {
            'total_proxies': 0,
            'active_proxies': 0,
            'proxy_types': {},
            'regions': {},
            'health_distribution': {'excellent': 0, 'good': 0, 'poor': 0, 'failed': 0},
            'usage_stats': self.usage_stats
        }

        for proxy_type in self.proxy_pools:
            stats['proxy_types'][proxy_type] = {'total': 0, 'active': 0}

            for region in self.proxy_pools[proxy_type]:
                if region not in stats['regions']:
                    stats['regions'][region] = {'total': 0, 'active': 0}

                for proxy in self.proxy_pools[proxy_type][region]:
                    stats['total_proxies'] += 1
                    stats['proxy_types'][proxy_type]['total'] += 1
                    stats['regions'][region]['total'] += 1

                    if proxy.is_active:
                        stats['active_proxies'] += 1
                        stats['proxy_types'][proxy_type]['active'] += 1
                        stats['regions'][region]['active'] += 1

                        # Health distribution
                        if proxy.health_score >= 0.8:
                            stats['health_distribution']['excellent'] += 1
                        elif proxy.health_score >= 0.6:
                            stats['health_distribution']['good'] += 1
                        elif proxy.health_score >= 0.3:
                            stats['health_distribution']['poor'] += 1
                        else:
                            stats['health_distribution']['failed'] += 1

        return stats

    def save_proxy_backup(self, filepath: str = "backup_proxies.json"):
        """Save current proxy pools to backup file"""
        try:
            backup_data = {}

            for proxy_type in self.proxy_pools:
                backup_data[proxy_type] = {}
                for region in self.proxy_pools[proxy_type]:
                    proxy_list = []
                    for proxy in self.proxy_pools[proxy_type][region]:
                        if proxy.is_active and proxy.health_score > 0.5:
                            proxy_dict = {
                                'host': proxy.host,
                                'port': proxy.port,
                                'username': proxy.username,
                                'password': proxy.password,
                                'protocol': proxy.protocol,
                                'region': proxy.region,
                                'proxy_type': proxy.proxy_type,
                                'provider': proxy.provider
                            }
                            proxy_list.append(proxy_dict)
                    backup_data[proxy_type][region] = proxy_list

            with open(filepath, 'w') as f:
                json.dump(backup_data, f, indent=2)

            logger.info(f"Proxy backup saved to {filepath}")

        except Exception as e:
            logger.error(f"Failed to save proxy backup: {e}")