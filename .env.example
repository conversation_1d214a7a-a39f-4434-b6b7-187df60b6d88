# Advanced Organic Traffic Generation System - Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# PROXY CONFIGURATION
# =============================================================================

# Primary Proxy Provider API Key
PROXY_API_KEY=your_proxy_api_key_here

# Backup Proxy Provider (optional)
BACKUP_PROXY_API_KEY=your_backup_proxy_api_key_here

# Proxy Provider URLs
PROXY_API_URL=https://api.your-proxy-provider.com/v1
BACKUP_PROXY_API_URL=https://api.backup-proxy-provider.com/v1

# =============================================================================
# DATABASE CONFIGURATION (for analytics and logging)
# =============================================================================

# Redis Configuration (for session management and caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# PostgreSQL Configuration (for detailed analytics)
DATABASE_URL=postgresql://username:password@localhost:5432/traffic_analytics

# =============================================================================
# MONITORING & ALERTS
# =============================================================================

# Email Configuration for Alerts
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
ALERT_EMAIL=<EMAIL>

# Slack Webhook for Notifications (optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# =============================================================================
# API KEYS FOR ADDITIONAL SERVICES
# =============================================================================

# IP Geolocation Service
IPGEOLOCATION_API_KEY=your_ipgeolocation_api_key

# User Agent Service (for fresh user agents)
USERAGENT_API_KEY=your_useragent_api_key

# Captcha Solving Service (if needed)
CAPTCHA_API_KEY=your_captcha_solving_api_key

# =============================================================================
# SECURITY & ENCRYPTION
# =============================================================================

# Encryption key for sensitive data
ENCRYPTION_KEY=your_32_character_encryption_key_here

# JWT Secret for API authentication
JWT_SECRET=your_jwt_secret_key_here

# =============================================================================
# DEVELOPMENT & DEBUGGING
# =============================================================================

# Environment Mode
ENVIRONMENT=production  # development, staging, production

# Debug Mode
DEBUG=false

# Log Level
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# =============================================================================
# RATE LIMITING & SAFETY
# =============================================================================

# Global Rate Limits
MAX_REQUESTS_PER_MINUTE=120
MAX_CONCURRENT_SESSIONS=10

# Safety Thresholds
MAX_DAILY_TRAFFIC=10000
EMERGENCY_STOP_THRESHOLD=0.8

# =============================================================================
# BROWSER CONFIGURATION
# =============================================================================

# Browser Binary Paths (optional, for custom browser installations)
CHROME_BINARY_PATH=/usr/bin/google-chrome
FIREFOX_BINARY_PATH=/usr/bin/firefox

# Browser Data Directory
BROWSER_DATA_DIR=./browser_profiles

# =============================================================================
# ANALYTICS & REPORTING
# =============================================================================

# Google Analytics (for tracking your own website performance)
GA_TRACKING_ID=UA-XXXXXXXXX-X

# Custom Analytics Endpoint
ANALYTICS_ENDPOINT=https://your-analytics-service.com/api/events

# Report Export Settings
REPORT_EXPORT_PATH=./reports
REPORT_RETENTION_DAYS=30

# =============================================================================
# BACKUP & RECOVERY
# =============================================================================

# Backup Storage Configuration
BACKUP_STORAGE_TYPE=local  # local, s3, gcs
BACKUP_PATH=./backups

# AWS S3 Configuration (if using S3 for backups)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_BUCKET_NAME=your-backup-bucket
AWS_REGION=us-east-1

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================

# Worker Configuration
WORKER_PROCESSES=4
WORKER_THREADS=8

# Memory Limits
MAX_MEMORY_USAGE=2048  # MB

# Cache Configuration
CACHE_TTL=3600  # seconds
CACHE_MAX_SIZE=1000  # number of items

# =============================================================================
# COMPLIANCE & LEGAL
# =============================================================================

# Company Information (for compliance)
COMPANY_NAME=Your Company Name
COMPANY_EMAIL=<EMAIL>
PRIVACY_POLICY_URL=https://yourcompany.com/privacy
TERMS_OF_SERVICE_URL=https://yourcompany.com/terms

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/Disable Features
ENABLE_MOBILE_SIMULATION=true
ENABLE_CANVAS_FINGERPRINTING=true
ENABLE_WEBGL_FINGERPRINTING=true
ENABLE_AUDIO_FINGERPRINTING=false
ENABLE_GEOLOCATION_SPOOFING=true
ENABLE_TIMEZONE_SPOOFING=true

# Experimental Features
ENABLE_AI_BEHAVIOR_LEARNING=false
ENABLE_ADVANCED_CAPTCHA_SOLVING=false
ENABLE_SOCIAL_MEDIA_INTEGRATION=false
