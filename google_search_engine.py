"""
Ultra-Realistic Google Search Behavior Engine

This module implements advanced Google search patterns that perfectly mimic
human search behavior including SERP interactions, natural query variations,
and realistic browsing patterns.
"""

import asyncio
import random
import re
from typing import Dict, List, Optional, Tuple, Any
from urllib.parse import quote_plus
from playwright.async_api import Page, <PERSON><PERSON><PERSON><PERSON><PERSON>
from loguru import logger
from behavior_simulator import BehaviorSimulator

class GoogleSearchEngine:
    """Ultra-realistic Google search behavior engine"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Google search engine"""
        self.config = config
        self.behavior_simulator = BehaviorSimulator()
        
        # Google-specific selectors (updated for 2024)
        self.selectors = {
            'search_box': 'textarea[name="q"], input[name="q"]',
            'search_button': 'input[name="btnK"], button[type="submit"]',
            'results_container': '#search, #rso',
            'result_links': 'h3 a, .yuRUbf a, .LC20lb',
            'result_titles': 'h3, .LC20lb',
            'result_snippets': '.VwiC3b, .s3v9rd',
            'related_searches': '#brs a, .k8XOCe a',
            'people_also_ask': '.related-question-pair, .JlqpRe',
            'next_page': 'a[aria-label="Next"], #pnnext',
            'autocomplete': '.wM6W7d span, .sbct',
            'search_tools': '#hdtb-tls',
            'filters': '.hdtb-mitem'
        }
        
        # Common typos and variations
        self.typo_patterns = {
            'e': ['3', 'w', 'r'],
            'a': ['s', 'q'],
            'o': ['i', 'p', '0'],
            'i': ['u', 'o', '8'],
            'u': ['y', 'i'],
            't': ['r', 'y', '5'],
            'r': ['e', 't', '4'],
            's': ['a', 'd'],
            'd': ['s', 'f'],
            'f': ['d', 'g'],
            'g': ['f', 'h'],
            'h': ['g', 'j'],
            'j': ['h', 'k'],
            'k': ['j', 'l'],
            'l': ['k', ';'],
            'n': ['b', 'm'],
            'm': ['n', ',']
        }
        
        logger.info("Ultra-realistic Google search engine initialized")
    
    async def execute_search_session(self, page: Page, keyword: str, 
                                   target_url: str, session_type: str = "impression") -> Dict[str, Any]:
        """Execute ultra-realistic Google search session"""
        session_result = {
            'keyword': keyword,
            'target_url': target_url,
            'session_type': session_type,
            'success': False,
            'impressions_generated': 0,
            'clicked_target': False,
            'serp_interactions': [],
            'search_refinements': 0,
            'time_on_serp': 0,
            'competitor_clicks': 0
        }
        
        try:
            # Navigate to Google with realistic approach
            await self._navigate_to_google(page)
            
            # Generate realistic search query
            search_query = self._generate_realistic_query(keyword)
            session_result['actual_query'] = search_query
            
            # Perform search with human-like behavior
            await self._perform_realistic_search(page, search_query)
            
            # Interact with SERP (Search Engine Results Page)
            serp_result = await self._interact_with_serp(page, target_url, session_type)
            session_result.update(serp_result)
            
            # Handle different session types
            if session_type == "click" and session_result['target_found']:
                click_result = await self._execute_click_session(page, target_url)
                session_result.update(click_result)
            elif session_type == "impression":
                # For impression sessions, just view and interact with SERP
                await self._generate_impression_interactions(page)
                session_result['impressions_generated'] = 1
            
            session_result['success'] = True
            return session_result
            
        except Exception as e:
            logger.error(f"Error in Google search session: {e}")
            session_result['error'] = str(e)
            return session_result
    
    async def _navigate_to_google(self, page: Page):
        """Navigate to Google with realistic behavior"""
        try:
            # Sometimes users go to google.com, sometimes they use address bar
            if random.random() < 0.7:  # 70% go to google.com directly
                await page.goto("https://www.google.com", wait_until='networkidle')
            else:  # 30% use address bar search
                await page.goto("https://www.google.com/search?q=", wait_until='networkidle')
            
            # Handle cookie consent and privacy banners
            await self._handle_google_banners(page)
            
            # Wait for search box to be ready
            await page.wait_for_selector(self.selectors['search_box'], timeout=10000)
            
            # Sometimes users interact with the page before searching
            if random.random() < 0.2:  # 20% chance
                await self._casual_page_interaction(page)
            
        except Exception as e:
            logger.error(f"Failed to navigate to Google: {e}")
            raise
    
    async def _handle_google_banners(self, page: Page):
        """Handle Google's cookie and privacy banners"""
        try:
            # Google's "I agree" button for EU users
            consent_selectors = [
                'button:has-text("I agree")',
                'button:has-text("Accept all")',
                '#L2AGLb',  # Google's specific consent button
                '.QS5gu',   # Another Google consent element
                'button[id*="accept"]',
                'button[aria-label*="Accept"]'
            ]
            
            for selector in consent_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=2000)
                    if element:
                        await element.click()
                        await asyncio.sleep(random.uniform(0.5, 1.5))
                        logger.debug("Handled Google consent banner")
                        break
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"No Google banner found or error handling: {e}")
    
    async def _casual_page_interaction(self, page: Page):
        """Simulate casual page interactions before searching"""
        try:
            # Random interactions that humans do
            interactions = [
                'scroll_slightly',
                'move_mouse_around',
                'hover_elements',
                'check_other_google_services'
            ]
            
            interaction = random.choice(interactions)
            
            if interaction == 'scroll_slightly':
                await page.mouse.wheel(0, random.randint(50, 200))
                await asyncio.sleep(random.uniform(0.5, 1.5))
            
            elif interaction == 'move_mouse_around':
                for _ in range(random.randint(2, 4)):
                    x = random.randint(100, 800)
                    y = random.randint(100, 600)
                    await page.mouse.move(x, y)
                    await asyncio.sleep(random.uniform(0.3, 0.8))
            
            elif interaction == 'hover_elements':
                # Hover over Google services links
                try:
                    elements = await page.query_selector_all('a[href*="gmail"], a[href*="images"], a[href*="maps"]')
                    if elements:
                        element = random.choice(elements)
                        await element.hover()
                        await asyncio.sleep(random.uniform(0.5, 2.0))
                except:
                    pass
            
            elif interaction == 'check_other_google_services':
                # Sometimes click on Images or News tabs
                try:
                    tabs = await page.query_selector_all('a[href*="tbm=isch"], a[href*="tbm=nws"]')
                    if tabs and random.random() < 0.3:  # 30% chance to click
                        tab = random.choice(tabs)
                        await tab.click()
                        await asyncio.sleep(random.uniform(1, 3))
                        await page.go_back()  # Go back to main search
                        await asyncio.sleep(random.uniform(0.5, 1.5))
                except:
                    pass
                    
        except Exception as e:
            logger.debug(f"Error in casual page interaction: {e}")
    
    def _generate_realistic_query(self, keyword: str) -> str:
        """Generate realistic search query with natural variations"""
        try:
            # Apply various realistic modifications
            query = keyword
            
            # 5% chance of typos
            if random.random() < 0.05:
                query = self._add_realistic_typo(query)
            
            # 15% chance of adding natural modifiers
            if random.random() < 0.15:
                modifiers = [
                    "best", "top", "good", "reliable", "professional",
                    "near me", "in my area", "local", "reviews",
                    "2024", "latest", "new", "updated"
                ]
                modifier = random.choice(modifiers)
                if random.random() < 0.5:
                    query = f"{modifier} {query}"
                else:
                    query = f"{query} {modifier}"
            
            # 10% chance of adding questions words
            if random.random() < 0.10:
                question_words = ["what is", "how to", "where to find", "why choose"]
                question = random.choice(question_words)
                query = f"{question} {query}"
            
            # 8% chance of making it more conversational
            if random.random() < 0.08:
                conversational = [
                    f"I need {query}",
                    f"looking for {query}",
                    f"find {query}",
                    f"search {query}"
                ]
                query = random.choice(conversational)
            
            return query.strip()
            
        except Exception as e:
            logger.debug(f"Error generating realistic query: {e}")
            return keyword
    
    def _add_realistic_typo(self, text: str) -> str:
        """Add realistic typos to search query"""
        try:
            if len(text) < 3:
                return text
            
            # Choose random position (avoid first and last character)
            pos = random.randint(1, len(text) - 2)
            char = text[pos].lower()
            
            if char in self.typo_patterns:
                replacement = random.choice(self.typo_patterns[char])
                text = text[:pos] + replacement + text[pos + 1:]
            
            return text
            
        except Exception as e:
            logger.debug(f"Error adding typo: {e}")
            return text
    
    async def _perform_realistic_search(self, page: Page, query: str):
        """Perform search with ultra-realistic human behavior"""
        try:
            # Click on search box
            search_box = await page.wait_for_selector(self.selectors['search_box'])
            await search_box.click()
            
            # Clear any existing text
            await page.keyboard.press('Control+a')
            await asyncio.sleep(random.uniform(0.1, 0.3))
            
            # Type with realistic behavior
            await self._type_with_autocomplete_behavior(page, query)
            
            # Submit search
            await self._submit_search(page)
            
            # Wait for results
            await page.wait_for_selector(self.selectors['results_container'], timeout=15000)
            
        except Exception as e:
            logger.error(f"Failed to perform search: {e}")
            raise
    
    async def _type_with_autocomplete_behavior(self, page: Page, query: str):
        """Type query with realistic autocomplete behavior"""
        try:
            # 70% of users interact with autocomplete
            if random.random() < 0.70:
                # Type partial query to trigger autocomplete
                partial_length = random.randint(3, min(len(query) - 1, 8))
                partial_query = query[:partial_length]
                
                # Type partial query
                for char in partial_query:
                    await page.keyboard.type(char)
                    await asyncio.sleep(random.uniform(0.05, 0.25))
                
                # Wait for autocomplete
                await asyncio.sleep(random.uniform(0.5, 1.5))
                
                # 50% chance to use autocomplete suggestion
                if random.random() < 0.50:
                    try:
                        suggestions = await page.query_selector_all(self.selectors['autocomplete'])
                        if suggestions:
                            # Sometimes users click on suggestion, sometimes use arrow keys
                            if random.random() < 0.7:  # 70% click
                                suggestion = random.choice(suggestions[:3])  # Top 3 suggestions
                                await suggestion.click()
                                return
                            else:  # 30% use arrow keys
                                await page.keyboard.press('ArrowDown')
                                await asyncio.sleep(random.uniform(0.2, 0.5))
                                await page.keyboard.press('Enter')
                                return
                    except:
                        pass
                
                # If autocomplete not used, complete typing
                remaining_query = query[partial_length:]
                for char in remaining_query:
                    await page.keyboard.type(char)
                    await asyncio.sleep(random.uniform(0.05, 0.20))
            else:
                # Type full query without autocomplete interaction
                for char in query:
                    await page.keyboard.type(char)
                    await asyncio.sleep(random.uniform(0.05, 0.20))
                    
        except Exception as e:
            logger.debug(f"Error in autocomplete behavior: {e}")
            # Fallback to simple typing
            await page.fill(self.selectors['search_box'], query)
    
    async def _submit_search(self, page: Page):
        """Submit search with realistic method"""
        try:
            # 80% use Enter key, 20% click search button
            if random.random() < 0.80:
                await page.keyboard.press('Enter')
            else:
                try:
                    search_button = await page.wait_for_selector(self.selectors['search_button'], timeout=3000)
                    await search_button.click()
                except:
                    # Fallback to Enter key
                    await page.keyboard.press('Enter')
                    
        except Exception as e:
            logger.debug(f"Error submitting search: {e}")
            await page.keyboard.press('Enter')
    
    async def _interact_with_serp(self, page: Page, target_url: str, 
                                session_type: str) -> Dict[str, Any]:
        """Interact with Search Engine Results Page"""
        result = {
            'target_found': False,
            'target_position': None,
            'serp_interactions': [],
            'time_on_serp': 0,
            'competitor_clicks': 0
        }
        
        try:
            start_time = asyncio.get_event_loop().time()
            
            # Initial SERP viewing behavior
            await self._initial_serp_scan(page)
            
            # Find target website in results
            target_info = await self._find_target_in_results(page, target_url)
            result.update(target_info)
            
            # Realistic SERP interactions
            interactions = await self._perform_serp_interactions(page, target_url, session_type)
            result['serp_interactions'] = interactions
            
            # Calculate time on SERP
            end_time = asyncio.get_event_loop().time()
            result['time_on_serp'] = end_time - start_time
            
            return result
            
        except Exception as e:
            logger.error(f"Error interacting with SERP: {e}")
            return result
    
    async def _initial_serp_scan(self, page: Page):
        """Simulate initial SERP scanning behavior"""
        try:
            # Humans typically scan the page first
            await asyncio.sleep(random.uniform(0.5, 2.0))
            
            # Scroll to see more results
            scroll_depth = random.uniform(0.3, 0.8)
            await self.behavior_simulator.simulate_scrolling(page, scroll_depth)
            
            # Pause to "read" results
            await asyncio.sleep(random.uniform(2, 6))
            
        except Exception as e:
            logger.debug(f"Error in initial SERP scan: {e}")
    
    async def _find_target_in_results(self, page: Page, target_url: str) -> Dict[str, Any]:
        """Find target website in search results"""
        try:
            from urllib.parse import urlparse
            target_domain = urlparse(target_url).netloc.lower()
            
            # Get all result links
            result_links = await page.query_selector_all(self.selectors['result_links'])
            
            for i, link in enumerate(result_links):
                try:
                    href = await link.get_attribute('href')
                    if href and target_domain in href.lower():
                        return {
                            'target_found': True,
                            'target_position': i + 1,
                            'target_element': link
                        }
                except:
                    continue
            
            return {'target_found': False, 'target_position': None}
            
        except Exception as e:
            logger.debug(f"Error finding target in results: {e}")
            return {'target_found': False, 'target_position': None}
    
    async def _perform_serp_interactions(self, page: Page, target_url: str, 
                                       session_type: str) -> List[str]:
        """Perform realistic SERP interactions"""
        interactions = []
        
        try:
            # Get all result elements
            result_links = await page.query_selector_all(self.selectors['result_links'])
            
            if not result_links:
                return interactions
            
            # Realistic interaction patterns
            interaction_patterns = [
                'hover_results',
                'read_snippets',
                'check_related_searches',
                'scroll_more',
                'click_competitor'
            ]
            
            # Perform 2-4 interactions
            num_interactions = random.randint(2, 4)
            selected_interactions = random.sample(interaction_patterns, 
                                                min(num_interactions, len(interaction_patterns)))
            
            for interaction in selected_interactions:
                if interaction == 'hover_results':
                    await self._hover_random_results(page, result_links)
                    interactions.append('hovered_results')
                
                elif interaction == 'read_snippets':
                    await self._read_result_snippets(page)
                    interactions.append('read_snippets')
                
                elif interaction == 'check_related_searches':
                    await self._check_related_searches(page)
                    interactions.append('checked_related_searches')
                
                elif interaction == 'scroll_more':
                    await self._scroll_for_more_results(page)
                    interactions.append('scrolled_more')
                
                elif interaction == 'click_competitor' and session_type == 'impression':
                    # Only click competitors in impression sessions
                    clicked = await self._click_competitor_result(page, target_url, result_links)
                    if clicked:
                        interactions.append('clicked_competitor')
            
            return interactions
            
        except Exception as e:
            logger.debug(f"Error in SERP interactions: {e}")
            return interactions
    
    async def _hover_random_results(self, page: Page, result_links: List[ElementHandle]):
        """Hover over random search results"""
        try:
            # Hover over 2-3 random results
            num_hovers = random.randint(2, min(3, len(result_links)))
            hover_targets = random.sample(result_links, num_hovers)
            
            for link in hover_targets:
                await link.hover()
                await asyncio.sleep(random.uniform(0.5, 2.0))
                
        except Exception as e:
            logger.debug(f"Error hovering results: {e}")
    
    async def _read_result_snippets(self, page: Page):
        """Simulate reading result snippets"""
        try:
            snippets = await page.query_selector_all(self.selectors['result_snippets'])
            
            if snippets:
                # "Read" 2-3 snippets
                num_reads = random.randint(2, min(3, len(snippets)))
                read_targets = random.sample(snippets, num_reads)
                
                for snippet in read_targets:
                    await snippet.scroll_into_view_if_needed()
                    await asyncio.sleep(random.uniform(1.5, 4.0))  # Reading time
                    
        except Exception as e:
            logger.debug(f"Error reading snippets: {e}")
    
    async def _check_related_searches(self, page: Page):
        """Check related searches section"""
        try:
            # Scroll to bottom to see related searches
            await page.evaluate('window.scrollTo(0, document.body.scrollHeight)')
            await asyncio.sleep(random.uniform(1, 2))
            
            related_searches = await page.query_selector_all(self.selectors['related_searches'])
            
            if related_searches:
                # Sometimes hover over related searches
                if random.random() < 0.5:
                    target = random.choice(related_searches[:3])
                    await target.hover()
                    await asyncio.sleep(random.uniform(1, 3))
                    
        except Exception as e:
            logger.debug(f"Error checking related searches: {e}")
    
    async def _scroll_for_more_results(self, page: Page):
        """Scroll to see more results"""
        try:
            # Scroll down to see more results
            for _ in range(random.randint(2, 4)):
                await page.mouse.wheel(0, random.randint(200, 500))
                await asyncio.sleep(random.uniform(0.5, 1.5))
                
        except Exception as e:
            logger.debug(f"Error scrolling for more results: {e}")
    
    async def _click_competitor_result(self, page: Page, target_url: str, 
                                     result_links: List[ElementHandle]) -> bool:
        """Click on competitor result (for impression sessions)"""
        try:
            from urllib.parse import urlparse
            target_domain = urlparse(target_url).netloc.lower()
            
            # Find non-target results
            competitor_links = []
            for link in result_links[:5]:  # Only top 5 results
                try:
                    href = await link.get_attribute('href')
                    if href and target_domain not in href.lower():
                        competitor_links.append(link)
                except:
                    continue
            
            if competitor_links and random.random() < 0.15:  # 15% chance
                competitor = random.choice(competitor_links)
                await competitor.click()
                
                # Stay on competitor site briefly
                await asyncio.sleep(random.uniform(5, 15))
                
                # Go back to search results
                await page.go_back()
                await asyncio.sleep(random.uniform(1, 3))
                
                return True
                
            return False
            
        except Exception as e:
            logger.debug(f"Error clicking competitor: {e}")
            return False
    
    async def _execute_click_session(self, page: Page, target_url: str) -> Dict[str, Any]:
        """Execute click session on target website"""
        result = {
            'clicked_target': False,
            'pages_visited': 0,
            'time_on_site': 0,
            'bounce': True
        }
        
        try:
            # Find and click target link
            target_info = await self._find_target_in_results(page, target_url)
            
            if target_info['target_found']:
                # Click target with realistic behavior
                await self._click_target_with_behavior(page, target_info['target_element'])
                
                # Wait for page load
                await page.wait_for_load_state('networkidle', timeout=15000)
                
                # Verify we're on target site
                current_url = page.url
                from urllib.parse import urlparse
                target_domain = urlparse(target_url).netloc.lower()
                
                if target_domain in current_url.lower():
                    result['clicked_target'] = True
                    
                    # Browse target website
                    browsing_result = await self._browse_target_website(page, target_url)
                    result.update(browsing_result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in click session: {e}")
            return result
    
    async def _click_target_with_behavior(self, page: Page, target_element: ElementHandle):
        """Click target with realistic human behavior"""
        try:
            # Scroll to make target visible
            await target_element.scroll_into_view_if_needed()
            
            # Hover before clicking (realistic behavior)
            await target_element.hover()
            await asyncio.sleep(random.uniform(0.5, 2.0))
            
            # Click with slight randomness in position
            box = await target_element.bounding_box()
            if box:
                click_x = box['x'] + box['width'] * random.uniform(0.2, 0.8)
                click_y = box['y'] + box['height'] * random.uniform(0.3, 0.7)
                await page.mouse.click(click_x, click_y)
            else:
                await target_element.click()
                
        except Exception as e:
            logger.debug(f"Error clicking target: {e}")
            await target_element.click()  # Fallback
    
    async def _browse_target_website(self, page: Page, target_url: str) -> Dict[str, Any]:
        """Browse target website with realistic behavior"""
        result = {
            'pages_visited': 1,
            'time_on_site': 0,
            'bounce': True
        }
        
        try:
            start_time = asyncio.get_event_loop().time()
            
            # Initial page reading
            reading_time = random.randint(30, 120)
            await self.behavior_simulator.simulate_reading_behavior(page, reading_time)
            
            # Visit additional pages (high engagement - 90% multi-page visits)
            # 90% chance of visiting multiple pages (10% bounce rate)
            if random.random() < 0.90:
                additional_pages = random.randint(3, 6)  # 3-6 additional pages for high engagement

                for i in range(additional_pages):
                    if await self._visit_internal_page(page, target_url):
                        result['pages_visited'] += 1
                        result['bounce'] = False

                        # Extended reading time per page (30-60 seconds each)
                        page_reading_time = random.randint(30, 60)
                        await self.behavior_simulator.simulate_reading_behavior(page, page_reading_time)
                    else:
                        break
            else:
                # 10% bounce rate - single page visit only
                result['bounce'] = True
            
            # Calculate total time on site
            end_time = asyncio.get_event_loop().time()
            result['time_on_site'] = end_time - start_time
            
            return result
            
        except Exception as e:
            logger.error(f"Error browsing target website: {e}")
            return result
    
    async def _visit_internal_page(self, page: Page, target_url: str) -> bool:
        """Visit an internal page on target website"""
        try:
            from urllib.parse import urlparse
            target_domain = urlparse(target_url).netloc
            
            # Find internal links
            internal_links = await page.query_selector_all(f'a[href*="{target_domain}"], a[href^="/"]')
            
            if not internal_links:
                return False
            
            # Filter out unwanted links
            good_links = []
            unwanted_patterns = ['logout', 'admin', 'login', 'register', 'cart', 'checkout', 'contact']
            
            for link in internal_links:
                try:
                    href = await link.get_attribute('href')
                    text = await link.text_content()
                    
                    if href and text and len(text.strip()) > 2:
                        if not any(pattern in href.lower() or pattern in text.lower() 
                                 for pattern in unwanted_patterns):
                            good_links.append(link)
                except:
                    continue
            
            if not good_links:
                return False
            
            # Click random internal link
            selected_link = random.choice(good_links)
            await selected_link.click()
            await page.wait_for_load_state('networkidle', timeout=10000)
            
            # Verify still on target domain
            current_url = page.url
            return target_domain in current_url
            
        except Exception as e:
            logger.debug(f"Error visiting internal page: {e}")
            return False
    
    async def _generate_impression_interactions(self, page: Page):
        """Generate impression-only interactions"""
        try:
            # For impression sessions, just interact with SERP
            await asyncio.sleep(random.uniform(3, 8))
            
            # Maybe scroll a bit more
            if random.random() < 0.5:
                await page.mouse.wheel(0, random.randint(100, 300))
                await asyncio.sleep(random.uniform(1, 3))
            
            # Sometimes check next page
            if random.random() < 0.1:  # 10% chance
                try:
                    next_button = await page.query_selector(self.selectors['next_page'])
                    if next_button:
                        await next_button.click()
                        await page.wait_for_load_state('networkidle')
                        await asyncio.sleep(random.uniform(2, 5))
                        await page.go_back()
                except:
                    pass
                    
        except Exception as e:
            logger.debug(f"Error in impression interactions: {e}")
