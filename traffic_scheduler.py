"""
Intelligent Traffic Distribution & Scheduling System

This module implements intelligent traffic distribution across time periods,
keyword-based volume allocation, and realistic session timing patterns.
"""

import asyncio
import random
import schedule
from datetime import datetime, timedelta, time
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor
import threading
from loguru import logger
from config_manager import config

@dataclass
class TrafficSession:
    """Data class for individual traffic session"""
    session_id: str
    keyword: str
    priority: str  # primary, secondary, longtail
    scheduled_time: datetime
    region: str
    device_type: str
    proxy_type: str
    status: str = "pending"  # pending, running, completed, failed
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None

@dataclass
class DailyTrafficPlan:
    """Daily traffic distribution plan"""
    date: datetime
    total_sessions: int
    sessions_by_priority: Dict[str, int] = field(default_factory=dict)
    sessions_by_hour: Dict[int, int] = field(default_factory=dict)
    sessions_by_keyword: Dict[str, int] = field(default_factory=dict)
    batches: List[List[TrafficSession]] = field(default_factory=list)

class TrafficScheduler:
    """Intelligent traffic distribution and scheduling system"""
    
    def __init__(self):
        """Initialize traffic scheduler"""
        self.config = config
        self.keywords_config = config.keywords
        self.traffic_config = config.traffic
        self.scheduling_config = config.scheduling
        
        # Scheduling state
        self.daily_plans: Dict[str, DailyTrafficPlan] = {}
        self.active_sessions: Dict[str, TrafficSession] = {}
        self.completed_sessions: List[TrafficSession] = []
        
        # Execution control
        self.is_running = False
        self.executor = ThreadPoolExecutor(max_workers=self.scheduling_config.rate_limiting['max_concurrent'])
        self.session_counter = 0
        
        # Statistics
        self.stats = {
            'total_sessions_planned': 0,
            'total_sessions_executed': 0,
            'successful_sessions': 0,
            'failed_sessions': 0,
            'average_session_duration': 0.0
        }
        
        logger.info("Traffic scheduler initialized")
    
    def generate_daily_plan(self, target_date: datetime = None) -> DailyTrafficPlan:
        """Generate intelligent daily traffic distribution plan"""
        if target_date is None:
            target_date = datetime.now().date()
        
        try:
            # Calculate total sessions for the day
            daily_volume = self.traffic_config.daily_volume
            
            # Create daily plan
            plan = DailyTrafficPlan(
                date=target_date,
                total_sessions=daily_volume
            )
            
            # Distribute sessions by keyword priority
            self._distribute_by_priority(plan)
            
            # Distribute sessions across hours
            self._distribute_by_time(plan)
            
            # Create individual sessions
            self._create_session_schedule(plan)
            
            # Organize into batches
            self._organize_into_batches(plan)
            
            # Store plan
            date_key = target_date.isoformat()
            self.daily_plans[date_key] = plan
            
            self.stats['total_sessions_planned'] += daily_volume
            
            logger.info(f"Generated daily plan for {target_date}: {daily_volume} sessions")
            return plan
            
        except Exception as e:
            logger.error(f"Error generating daily plan: {e}")
            raise
    
    def _distribute_by_priority(self, plan: DailyTrafficPlan):
        """Distribute sessions by keyword priority"""
        distribution = self.traffic_config.distribution
        
        plan.sessions_by_priority = {
            'primary': int(plan.total_sessions * distribution['primary']),
            'secondary': int(plan.total_sessions * distribution['secondary']),
            'longtail': int(plan.total_sessions * distribution['longtail'])
        }
        
        # Adjust for rounding errors
        total_allocated = sum(plan.sessions_by_priority.values())
        if total_allocated < plan.total_sessions:
            plan.sessions_by_priority['primary'] += plan.total_sessions - total_allocated
    
    def _distribute_by_time(self, plan: DailyTrafficPlan):
        """Distribute sessions across operating hours"""
        operating_hours = self.scheduling_config.operating_hours
        start_hour = operating_hours['start']
        end_hour = operating_hours['end']
        
        # Create realistic hourly distribution (bell curve with peaks)
        hours = list(range(start_hour, end_hour + 1))
        
        # Generate weights for each hour (simulate real user behavior)
        weights = []
        for hour in hours:
            if 9 <= hour <= 11:  # Morning peak
                weight = random.uniform(1.2, 1.5)
            elif 13 <= hour <= 15:  # Afternoon peak
                weight = random.uniform(1.1, 1.4)
            elif 19 <= hour <= 21:  # Evening peak
                weight = random.uniform(1.0, 1.3)
            elif hour < 9 or hour > 21:  # Off-peak hours
                weight = random.uniform(0.3, 0.6)
            else:  # Regular hours
                weight = random.uniform(0.8, 1.1)
            
            weights.append(weight)
        
        # Normalize weights
        total_weight = sum(weights)
        normalized_weights = [w / total_weight for w in weights]
        
        # Distribute sessions
        remaining_sessions = plan.total_sessions
        for i, hour in enumerate(hours[:-1]):  # All except last hour
            sessions_this_hour = int(plan.total_sessions * normalized_weights[i])
            plan.sessions_by_hour[hour] = sessions_this_hour
            remaining_sessions -= sessions_this_hour
        
        # Assign remaining sessions to last hour
        plan.sessions_by_hour[hours[-1]] = remaining_sessions
    
    def _create_session_schedule(self, plan: DailyTrafficPlan):
        """Create individual session schedules"""
        sessions = []
        
        # Get all keywords by priority
        all_keywords = {
            'primary': self.keywords_config.primary,
            'secondary': self.keywords_config.secondary,
            'longtail': self.keywords_config.longtail
        }
        
        # Create sessions for each priority level
        for priority, session_count in plan.sessions_by_priority.items():
            keywords = all_keywords[priority]
            if not keywords:
                continue
            
            # Distribute sessions among keywords in this priority
            sessions_per_keyword = session_count // len(keywords)
            extra_sessions = session_count % len(keywords)
            
            for i, keyword in enumerate(keywords):
                keyword_sessions = sessions_per_keyword
                if i < extra_sessions:
                    keyword_sessions += 1
                
                plan.sessions_by_keyword[keyword] = keyword_sessions
                
                # Create individual sessions for this keyword
                for _ in range(keyword_sessions):
                    session = self._create_individual_session(plan, keyword, priority)
                    sessions.append(session)
        
        # Sort sessions by scheduled time
        sessions.sort(key=lambda s: s.scheduled_time)
        plan.sessions = sessions
    
    def _create_individual_session(self, plan: DailyTrafficPlan, keyword: str, 
                                 priority: str) -> TrafficSession:
        """Create individual traffic session"""
        self.session_counter += 1
        session_id = f"session_{plan.date.strftime('%Y%m%d')}_{self.session_counter:06d}"
        
        # Select random hour based on distribution
        hour_weights = [(hour, count) for hour, count in plan.sessions_by_hour.items()]
        selected_hour = random.choices(
            [hw[0] for hw in hour_weights],
            weights=[hw[1] for hw in hour_weights]
        )[0]
        
        # Random minute and second within the hour
        minute = random.randint(0, 59)
        second = random.randint(0, 59)
        
        scheduled_time = datetime.combine(
            plan.date,
            time(selected_hour, minute, second)
        )
        
        # Select region (prefer primary regions)
        regions = config.regions
        if random.random() < 0.8:  # 80% primary regions
            region = random.choice(regions.get('primary', ['US']))
        else:
            region = random.choice(regions.get('secondary', ['DE']))
        
        # Select device type based on configuration
        device_distribution = config.fingerprinting.get('device_distribution', {})
        if random.random() < device_distribution.get('mobile', 0.6):
            device_type = 'mobile'
        else:
            device_type = 'desktop'
        
        # Select proxy type
        proxy_types = config.proxy.types
        proxy_type = random.choice(proxy_types)
        
        return TrafficSession(
            session_id=session_id,
            keyword=keyword,
            priority=priority,
            scheduled_time=scheduled_time,
            region=region,
            device_type=device_type,
            proxy_type=proxy_type
        )
    
    def _organize_into_batches(self, plan: DailyTrafficPlan):
        """Organize sessions into execution batches"""
        sessions_per_day = self.scheduling_config.sessions_per_day
        num_batches = random.randint(sessions_per_day[0], sessions_per_day[1])
        
        # Calculate batch size
        base_batch_size = plan.total_sessions // num_batches
        variance = self.scheduling_config.batch_size_variance
        
        batches = []
        remaining_sessions = plan.sessions.copy()
        
        for i in range(num_batches):
            if i == num_batches - 1:  # Last batch gets remaining sessions
                batch_size = len(remaining_sessions)
            else:
                # Apply variance to batch size
                variance_factor = random.uniform(1 - variance, 1 + variance)
                batch_size = max(1, int(base_batch_size * variance_factor))
                batch_size = min(batch_size, len(remaining_sessions))
            
            if batch_size > 0:
                batch = remaining_sessions[:batch_size]
                remaining_sessions = remaining_sessions[batch_size:]
                batches.append(batch)
        
        plan.batches = batches
        logger.debug(f"Organized {plan.total_sessions} sessions into {len(batches)} batches")
    
    async def execute_daily_plan(self, target_date: datetime = None):
        """Execute daily traffic plan"""
        if target_date is None:
            target_date = datetime.now().date()
        
        date_key = target_date.isoformat()
        
        if date_key not in self.daily_plans:
            logger.info(f"No plan found for {target_date}, generating new plan")
            self.generate_daily_plan(target_date)
        
        plan = self.daily_plans[date_key]
        
        try:
            self.is_running = True
            logger.info(f"Starting execution of daily plan for {target_date}")
            
            # Execute batches throughout the day
            for i, batch in enumerate(plan.batches):
                if not self.is_running:
                    break
                
                logger.info(f"Executing batch {i+1}/{len(plan.batches)} with {len(batch)} sessions")
                
                # Execute batch
                await self._execute_batch(batch)
                
                # Wait between batches (except for the last one)
                if i < len(plan.batches) - 1:
                    delay_range = self.scheduling_config.rate_limiting['delay_between_sessions']
                    delay = random.randint(delay_range[0], delay_range[1])
                    logger.debug(f"Waiting {delay} seconds before next batch")
                    await asyncio.sleep(delay)
            
            logger.info(f"Completed daily plan execution for {target_date}")
            
        except Exception as e:
            logger.error(f"Error executing daily plan: {e}")
            raise
        finally:
            self.is_running = False
    
    async def _execute_batch(self, batch: List[TrafficSession]):
        """Execute a batch of traffic sessions"""
        try:
            # Limit concurrent sessions
            max_concurrent = self.scheduling_config.rate_limiting['max_concurrent']
            semaphore = asyncio.Semaphore(max_concurrent)
            
            # Create tasks for all sessions in batch
            tasks = []
            for session in batch:
                task = asyncio.create_task(self._execute_session_with_semaphore(session, semaphore))
                tasks.append(task)
            
            # Wait for all sessions to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for session, result in zip(batch, results):
                if isinstance(result, Exception):
                    logger.error(f"Session {session.session_id} failed: {result}")
                    session.status = "failed"
                    self.stats['failed_sessions'] += 1
                else:
                    session.status = "completed"
                    session.result = result
                    self.stats['successful_sessions'] += 1
                
                self.completed_sessions.append(session)
                self.stats['total_sessions_executed'] += 1
            
        except Exception as e:
            logger.error(f"Error executing batch: {e}")
            raise
    
    async def _execute_session_with_semaphore(self, session: TrafficSession, 
                                            semaphore: asyncio.Semaphore):
        """Execute individual session with concurrency control"""
        async with semaphore:
            return await self._execute_individual_session(session)
    
    async def _execute_individual_session(self, session: TrafficSession) -> Dict[str, Any]:
        """Execute individual traffic session"""
        try:
            session.start_time = datetime.now()
            session.status = "running"
            self.active_sessions[session.session_id] = session
            
            logger.debug(f"Starting session {session.session_id} for keyword: {session.keyword}")
            
            # Import here to avoid circular imports
            from advanced_traffic_generator import AdvancedTrafficGenerator
            
            # Create traffic generator instance
            generator = AdvancedTrafficGenerator()
            
            # Execute session
            result = await generator.execute_session(
                keyword=session.keyword,
                region=session.region,
                device_type=session.device_type,
                proxy_type=session.proxy_type
            )
            
            session.end_time = datetime.now()
            session.result = result
            
            # Remove from active sessions
            self.active_sessions.pop(session.session_id, None)
            
            logger.debug(f"Completed session {session.session_id}: {result.get('success', False)}")
            return result
            
        except Exception as e:
            session.end_time = datetime.now()
            session.status = "failed"
            self.active_sessions.pop(session.session_id, None)
            logger.error(f"Session {session.session_id} failed: {e}")
            raise
    
    def get_schedule_statistics(self) -> Dict[str, Any]:
        """Get comprehensive scheduling statistics"""
        # Calculate average session duration
        completed_with_duration = [
            s for s in self.completed_sessions 
            if s.start_time and s.end_time
        ]
        
        if completed_with_duration:
            total_duration = sum(
                (s.end_time - s.start_time).total_seconds() 
                for s in completed_with_duration
            )
            avg_duration = total_duration / len(completed_with_duration)
            self.stats['average_session_duration'] = avg_duration
        
        return {
            'statistics': self.stats.copy(),
            'active_sessions': len(self.active_sessions),
            'daily_plans': len(self.daily_plans),
            'is_running': self.is_running,
            'session_counter': self.session_counter
        }
    
    def stop_execution(self):
        """Stop traffic execution"""
        self.is_running = False
        logger.info("Traffic execution stopped")
    
    def cleanup(self):
        """Cleanup scheduler resources"""
        self.stop_execution()
        self.executor.shutdown(wait=True)
        logger.info("Traffic scheduler cleanup completed")
