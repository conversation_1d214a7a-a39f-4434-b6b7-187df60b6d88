"""
Advanced Browser Manager with Anti-Detection Capabilities

This module provides sophisticated browser automation with advanced evasion techniques
to simulate human-like browsing behavior and avoid detection.
"""

import asyncio
import random
import json
import base64
from typing import Dict, Any, Optional, List
from pathlib import Path
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontex<PERSON>, Page
from playwright_stealth import stealth_async
from loguru import logger
from config_manager import config

class BrowserManager:
    """Advanced browser manager with anti-detection capabilities"""
    
    def __init__(self):
        """Initialize browser manager"""
        self.browser: Optional[Browser] = None
        self.contexts: List[BrowserContext] = []
        self.playwright = None
        self.browser_data_dir = Path(config.get_environment_config()['browser_data_dir'])
        self.browser_data_dir.mkdir(exist_ok=True)
    
    async def initialize(self):
        """Initialize Playwright and browser"""
        try:
            self.playwright = await async_playwright().start()
            logger.info("Playwright initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Playwright: {e}")
            raise
    
    async def create_stealth_browser(self, proxy_config: Optional[Dict] = None, 
                                   headless: bool = True) -> Browser:
        """Create a browser instance with stealth capabilities"""
        try:
            # Browser launch arguments for maximum stealth
            launch_args = [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-features=TranslateUI',
                '--disable-ipc-flooding-protection',
                '--disable-background-networking',
                '--disable-default-apps',
                '--disable-extensions',
                '--disable-sync',
                '--metrics-recording-only',
                '--no-default-browser-check',
                '--no-pings',
                '--password-store=basic',
                '--use-mock-keychain',
                '--disable-component-extensions-with-background-pages',
                '--disable-blink-features=AutomationControlled',
                '--exclude-switches=enable-automation',
                '--disable-web-security',
                '--allow-running-insecure-content',
                '--disable-features=VizDisplayCompositor'
            ]
            
            # Add random window size for desktop browsers
            if not headless:
                width = random.randint(1200, 1920)
                height = random.randint(800, 1080)
                launch_args.append(f'--window-size={width},{height}')
            
            # Browser launch options
            launch_options = {
                'headless': headless,
                'args': launch_args,
                'ignore_default_args': [
                    '--enable-blink-features=IdleDetection',
                    '--enable-automation'
                ]
            }
            
            # Add proxy configuration if provided
            if proxy_config:
                launch_options['proxy'] = {
                    'server': f"{proxy_config['protocol']}://{proxy_config['host']}:{proxy_config['port']}",
                    'username': proxy_config.get('username'),
                    'password': proxy_config.get('password')
                }
            
            # Launch browser
            self.browser = await self.playwright.chromium.launch(**launch_options)
            logger.info("Stealth browser created successfully")
            return self.browser
            
        except Exception as e:
            logger.error(f"Failed to create stealth browser: {e}")
            raise
    
    async def create_stealth_context(self, fingerprint: Dict[str, Any], 
                                   locale: str = "en-US") -> BrowserContext:
        """Create a browser context with advanced fingerprinting evasion"""
        try:
            # Context options based on fingerprint
            context_options = {
                'user_agent': fingerprint['user_agent'],
                'viewport': fingerprint['viewport'],
                'locale': locale,
                'timezone_id': fingerprint['timezone'],
                'device_scale_factor': fingerprint.get('device_scale_factor', 1.0),
                'is_mobile': fingerprint.get('is_mobile', False),
                'has_touch': fingerprint.get('is_mobile', False),
                'color_scheme': random.choice(['light', 'dark']),
                'reduced_motion': random.choice(['reduce', 'no-preference']),
                'forced_colors': 'none',
                'java_script_enabled': True,
                'accept_downloads': False,
                'ignore_https_errors': True,
                'bypass_csp': True
            }
            
            # Add geolocation if specified
            if 'geolocation' in fingerprint:
                context_options['geolocation'] = fingerprint['geolocation']
                context_options['permissions'] = ['geolocation']
            
            # Create context
            context = await self.browser.new_context(**context_options)
            
            # Apply stealth techniques
            await stealth_async(context)
            
            # Apply advanced fingerprinting evasion
            await self._apply_advanced_evasion(context, fingerprint)
            
            # Add to context list for cleanup
            self.contexts.append(context)
            
            logger.info("Stealth context created with advanced evasion")
            return context
            
        except Exception as e:
            logger.error(f"Failed to create stealth context: {e}")
            raise
    
    async def _apply_advanced_evasion(self, context: BrowserContext, 
                                    fingerprint: Dict[str, Any]):
        """Apply advanced fingerprinting evasion techniques"""
        
        # Advanced evasion script
        evasion_script = f"""
        // Remove webdriver property
        Object.defineProperty(navigator, 'webdriver', {{
            get: () => undefined,
        }});
        
        // Override plugins
        Object.defineProperty(navigator, 'plugins', {{
            get: () => [
                {{
                    name: 'Chrome PDF Plugin',
                    filename: 'internal-pdf-viewer',
                    description: 'Portable Document Format'
                }},
                {{
                    name: 'Chrome PDF Viewer',
                    filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai',
                    description: ''
                }},
                {{
                    name: 'Native Client',
                    filename: 'internal-nacl-plugin',
                    description: ''
                }}
            ]
        }});
        
        // Override languages
        Object.defineProperty(navigator, 'languages', {{
            get: () => {json.dumps(fingerprint.get('languages', ['en-US', 'en']))}
        }});
        
        // Override hardware concurrency
        Object.defineProperty(navigator, 'hardwareConcurrency', {{
            get: () => {fingerprint.get('hardware_concurrency', 4)}
        }});
        
        // Override device memory
        Object.defineProperty(navigator, 'deviceMemory', {{
            get: () => {fingerprint.get('device_memory', 8)}
        }});
        
        // Override connection
        Object.defineProperty(navigator, 'connection', {{
            get: () => ({{
                effectiveType: '{fingerprint.get('connection_type', '4g')}',
                rtt: {fingerprint.get('rtt', 50)},
                downlink: {fingerprint.get('downlink', 10)}
            }})
        }});
        
        // Canvas fingerprint randomization
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
        
        HTMLCanvasElement.prototype.toDataURL = function(type) {{
            const result = originalToDataURL.apply(this, arguments);
            // Add slight noise to canvas data
            const noise = Math.random() * 0.0001;
            return result.replace(/data:image\\/png;base64,/, 
                `data:image/png;base64,${{btoa(atob(result.split(',')[1]) + noise)}}`);
        }};
        
        CanvasRenderingContext2D.prototype.getImageData = function() {{
            const result = originalGetImageData.apply(this, arguments);
            // Add noise to image data
            for (let i = 0; i < result.data.length; i += 4) {{
                if (Math.random() < 0.001) {{
                    result.data[i] = Math.min(255, result.data[i] + Math.random() * 2 - 1);
                    result.data[i + 1] = Math.min(255, result.data[i + 1] + Math.random() * 2 - 1);
                    result.data[i + 2] = Math.min(255, result.data[i + 2] + Math.random() * 2 - 1);
                }}
            }}
            return result;
        }};
        
        // WebGL fingerprint randomization
        const getParameterProxyHandler = {{
            apply: function(target, thisArg, args) {{
                const param = args[0];
                let result = Reflect.apply(target, thisArg, args);
                
                // Randomize specific WebGL parameters
                if (param === 37445) {{ // UNMASKED_VENDOR_WEBGL
                    const vendors = ['Intel Inc.', 'NVIDIA Corporation', 'AMD'];
                    result = vendors[Math.floor(Math.random() * vendors.length)];
                }} else if (param === 37446) {{ // UNMASKED_RENDERER_WEBGL
                    const renderers = [
                        'Intel Iris OpenGL Engine',
                        'NVIDIA GeForce GTX 1060',
                        'AMD Radeon Pro 560'
                    ];
                    result = renderers[Math.floor(Math.random() * renderers.length)];
                }}
                
                return result;
            }}
        }};
        
        if (WebGLRenderingContext.prototype.getParameter) {{
            WebGLRenderingContext.prototype.getParameter = 
                new Proxy(WebGLRenderingContext.prototype.getParameter, getParameterProxyHandler);
        }}
        
        if (WebGL2RenderingContext.prototype.getParameter) {{
            WebGL2RenderingContext.prototype.getParameter = 
                new Proxy(WebGL2RenderingContext.prototype.getParameter, getParameterProxyHandler);
        }}
        
        // Audio context fingerprint randomization
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        if (AudioContext) {{
            const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
            AudioContext.prototype.createAnalyser = function() {{
                const analyser = originalCreateAnalyser.apply(this, arguments);
                const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                analyser.getFloatFrequencyData = function(array) {{
                    originalGetFloatFrequencyData.apply(this, arguments);
                    // Add slight noise to audio data
                    for (let i = 0; i < array.length; i++) {{
                        array[i] += Math.random() * 0.0001 - 0.00005;
                    }}
                }};
                return analyser;
            }};
        }}
        
        // Screen properties randomization
        Object.defineProperty(screen, 'colorDepth', {{
            get: () => {fingerprint.get('color_depth', 24)}
        }});
        
        Object.defineProperty(screen, 'pixelDepth', {{
            get: () => {fingerprint.get('color_depth', 24)}
        }});
        
        // Timezone spoofing
        const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
        Date.prototype.getTimezoneOffset = function() {{
            return {fingerprint.get('timezone_offset', 0)};
        }};
        
        // Battery API spoofing
        if (navigator.getBattery) {{
            navigator.getBattery = async () => ({{
                charging: {json.dumps(fingerprint.get('battery_charging', True))},
                chargingTime: {fingerprint.get('battery_charging_time', 0)},
                dischargingTime: {fingerprint.get('battery_discharging_time', 3600)},
                level: {fingerprint.get('battery_level', 0.8)}
            }});
        }}
        
        // Remove automation indicators
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        """
        
        # Add the evasion script to all pages in this context
        await context.add_init_script(evasion_script)
        
        # Set extra HTTP headers
        await context.set_extra_http_headers({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': fingerprint.get('accept_language', 'en-US,en;q=0.5'),
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })
        
        logger.debug("Advanced evasion techniques applied")
    
    async def create_stealth_page(self, context: BrowserContext) -> Page:
        """Create a new page with additional stealth measures"""
        try:
            page = await context.new_page()
            
            # Block unnecessary resources to improve performance and reduce detection
            await page.route("**/*", self._handle_route)
            
            # Override permissions
            await context.grant_permissions(['geolocation'])
            
            logger.debug("Stealth page created")
            return page
            
        except Exception as e:
            logger.error(f"Failed to create stealth page: {e}")
            raise
    
    async def _handle_route(self, route):
        """Handle resource routing to block unnecessary requests"""
        resource_type = route.request.resource_type
        
        # Block certain resource types to improve performance
        if resource_type in ['image', 'media', 'font', 'stylesheet']:
            # Randomly allow some resources to appear more natural
            if random.random() < 0.3:  # Allow 30% of blocked resources
                await route.continue_()
            else:
                await route.abort()
        else:
            await route.continue_()
    
    async def cleanup(self):
        """Clean up browser resources"""
        try:
            # Close all contexts
            for context in self.contexts:
                await context.close()
            self.contexts.clear()
            
            # Close browser
            if self.browser:
                await self.browser.close()
                self.browser = None
            
            # Stop playwright
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
            
            logger.info("Browser cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during browser cleanup: {e}")
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.cleanup()
