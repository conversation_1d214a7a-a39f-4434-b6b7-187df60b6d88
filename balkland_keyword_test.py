#!/usr/bin/env python3
"""
BALKLAND COMPREHENSIVE KEYWORD TEST
✅ Test all URL variations and keyword combinations
✅ Verify comprehensive coverage
"""

import random

class BalklandKeywordTest:
    def __init__(self):
        # URL variations
        self.url_variations = [
            'https://balkland.com/',
            'https://balkland.com',
            'balkland.com',
            'http://balkland.com',
            'https://www.balkland.com/',
            'www.balkland.com',
            'balkland'
        ]
        
        self.base_keywords = [
            'balkan tour', 'balkan tours', 'balkan vacation', 'balkan travel',
            'balkan trip', 'balkan packages', 'balkan adventure', 'balkan holiday',
            'tour packages', 'tour deals', 'luxury tours', 'private tours',
            'group tours', 'custom tours', 'adventure tours', 'guided tours',
            'Serbia tours', 'Croatia tours', 'Bosnia tours', 'Montenegro tours',
            'Albania tours', 'Macedonia tours', 'Slovenia tours', 'Bulgaria tours',
            'best tours', 'top tours', 'reviews', 'booking', 'book tour',
            '2025 tours', 'travel guide', 'vacation packages'
        ]
        
        # Generate ALL possible keyword combinations
        self.keywords = []
        
        # 1. URL variations alone
        for url in self.url_variations:
            self.keywords.append(url)
        
        # 2. URL variations + base keywords
        for url in self.url_variations:
            for keyword in self.base_keywords:
                self.keywords.append(f"{url} {keyword}")
                self.keywords.append(f"{keyword} {url}")
        
        # 3. Balkland + base keywords
        for keyword in self.base_keywords:
            self.keywords.append(f"Balkland {keyword}")
            self.keywords.append(f"{keyword} Balkland")
        
        # 4. Specific 2025 combinations
        year_keywords = [
            'Balkland balkan tour 2025', 'Balkland tour packages 2025', 'best Balkland tours 2025',
            'book Balkland tour 2025', 'Balkland tour deals 2025', 'luxury Balkland tours 2025',
            'private Balkland tours 2025', 'Balkland tour reviews 2025', 'Balkland balkan vacation 2025',
            'Balkland tours Serbia 2025', 'Balkland tours Croatia 2025', 'Balkland tours Bosnia 2025',
            'https://balkland.com/ balkan tour 2025', 'https://balkland.com balkan tour 2025',
            'http://balkland.com balkan tour 2025', 'www.balkland.com balkan tour 2025',
            'balkland.com balkan tour 2025', 'balkland balkan tour 2025'
        ]
        
        self.keywords.extend(year_keywords)
        
        # 5. URL + specific combinations (as requested)
        specific_combos = [
            'https://balkland.com/ balkan tour',
            'https://balkland.com balkan tour', 
            'http://balkland.com balkan tour',
            'https://www.balkland.com/ Balkan tour',
            'www.balkland.com Balkan tour',
            'balkland.com Balkan tour',
            'https://balkland.com/ Balkan tours',
            'https://balkland.com Balkan tours',
            'http://balkland.com Balkan tours',
            'https://www.balkland.com/ Balkan tours'
        ]
        
        self.keywords.extend(specific_combos)
        
        print("🚀 BALKLAND COMPREHENSIVE KEYWORD TEST")
        print("=" * 60)
        print(f"📊 TOTAL KEYWORDS: {len(self.keywords)}")
        print(f"🔗 URL VARIATIONS: {len(self.url_variations)}")
        print(f"🔍 BASE KEYWORDS: {len(self.base_keywords)}")
        print("=" * 60)
    
    def show_keyword_categories(self):
        """Show examples from each keyword category"""
        print("\n📊 KEYWORD CATEGORIES:")
        
        # URL variations alone
        print(f"\n1. 🔗 URL VARIATIONS ALONE ({len(self.url_variations)} keywords):")
        for url in self.url_variations:
            print(f"   • {url}")
        
        # URL + keyword combinations (show first 10)
        url_keyword_combos = []
        for url in self.url_variations[:2]:  # First 2 URLs
            for keyword in self.base_keywords[:3]:  # First 3 keywords
                url_keyword_combos.append(f"{url} {keyword}")
                url_keyword_combos.append(f"{keyword} {url}")
        
        print(f"\n2. 🔗 URL + KEYWORD COMBINATIONS (showing 12 of {len(self.url_variations) * len(self.base_keywords) * 2}):")
        for combo in url_keyword_combos:
            print(f"   • {combo}")
        
        # Balkland + keywords (show first 10)
        balkland_combos = []
        for keyword in self.base_keywords[:5]:
            balkland_combos.append(f"Balkland {keyword}")
            balkland_combos.append(f"{keyword} Balkland")
        
        print(f"\n3. 🏢 BALKLAND + KEYWORDS (showing 10 of {len(self.base_keywords) * 2}):")
        for combo in balkland_combos:
            print(f"   • {combo}")
        
        # Specific requested combinations
        specific_requested = [
            'https://balkland.com/ balkan tour',
            'https://balkland.com balkan tour', 
            'http://balkland.com balkan tour',
            'https://www.balkland.com/ Balkan tour',
            'www.balkland.com Balkan tour'
        ]
        
        print(f"\n4. 🎯 SPECIFIC REQUESTED COMBINATIONS:")
        for combo in specific_requested:
            print(f"   • {combo}")
        
        # 2025 combinations
        year_examples = [
            'Balkland balkan tour 2025',
            'https://balkland.com/ balkan tour 2025',
            'http://balkland.com balkan tour 2025',
            'balkland.com balkan tour 2025'
        ]
        
        print(f"\n5. 📅 2025 COMBINATIONS:")
        for combo in year_examples:
            print(f"   • {combo}")
    
    def test_random_keywords(self, count=20):
        """Test random keywords from the comprehensive list"""
        print(f"\n🧪 TESTING {count} RANDOM KEYWORDS:")
        
        random_keywords = random.sample(self.keywords, min(count, len(self.keywords)))
        
        for i, keyword in enumerate(random_keywords, 1):
            print(f"   {i:2d}. {keyword}")
            
            # Analyze keyword type
            keyword_type = "Unknown"
            if any(url in keyword for url in self.url_variations):
                if any(base in keyword for base in self.base_keywords):
                    keyword_type = "URL + Keyword Combo"
                else:
                    keyword_type = "URL Only"
            elif "Balkland" in keyword:
                keyword_type = "Balkland + Keyword"
            elif "2025" in keyword:
                keyword_type = "2025 Specific"
            
            print(f"       Type: {keyword_type}")
    
    def verify_requested_keywords(self):
        """Verify all specifically requested keywords are included"""
        print(f"\n✅ VERIFICATION OF REQUESTED KEYWORDS:")
        
        requested_keywords = [
            'https://balkland.com/',
            'https://balkland.com',
            'https://balkland.com/ balkan tour',
            'http://balkland.com balkan tour',
            'https://www.balkland.com/ Balkan tour'
        ]
        
        all_found = True
        for keyword in requested_keywords:
            if keyword in self.keywords:
                print(f"   ✅ FOUND: {keyword}")
            else:
                print(f"   ❌ MISSING: {keyword}")
                all_found = False
        
        if all_found:
            print(f"\n🎉 ALL REQUESTED KEYWORDS INCLUDED!")
        else:
            print(f"\n⚠️ Some requested keywords missing!")
        
        return all_found

if __name__ == "__main__":
    test = BalklandKeywordTest()
    
    test.show_keyword_categories()
    test.test_random_keywords(20)
    test.verify_requested_keywords()
    
    print(f"\n🎯 COMPREHENSIVE KEYWORD SYSTEM READY!")
    print(f"📊 Total Coverage: {len(test.keywords)} unique keyword variations")
    print(f"✅ All URL variations included")
    print(f"✅ All requested combinations included")
    print(f"💪 Ready for massive scale traffic generation!")
