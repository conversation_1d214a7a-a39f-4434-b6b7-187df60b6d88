"""
Intelligent Search & Navigation System

This module provides smart search execution with keyword targeting,
result page interaction, target website identification, and multi-page exploration.
"""

import asyncio
import random
import re
from typing import Dict, List, Optional, Tuple, Any
from urllib.parse import urlparse, urljoin
from playwright.async_api import Page, <PERSON><PERSON><PERSON><PERSON><PERSON>
from loguru import logger
from behavior_simulator import BehaviorSimulator
from config_manager import config

class SearchNavigator:
    """Intelligent search and navigation system"""
    
    def __init__(self):
        """Initialize search navigator"""
        self.target_config = config.target
        self.behavior_simulator = BehaviorSimulator()
        
        # Search engines configuration
        self.search_engines = {
            'google.com': {
                'url': 'https://www.google.com',
                'search_box': 'input[name="q"]',
                'search_button': 'input[name="btnK"]',
                'results_container': '#search',
                'result_links': 'h3 a, .yuRUbf a',
                'next_page': 'a[aria-label="Next"]'
            },
            'bing.com': {
                'url': 'https://www.bing.com',
                'search_box': 'input[name="q"]',
                'search_button': 'input[id="sb_form_go"]',
                'results_container': '#b_results',
                'result_links': '.b_title a',
                'next_page': '.sb_pagN'
            },
            'duckduckgo.com': {
                'url': 'https://duckduckgo.com',
                'search_box': 'input[name="q"]',
                'search_button': 'input[type="submit"]',
                'results_container': '#links',
                'result_links': '.result__title a',
                'next_page': '.sb_pagN'
            }
        }
        
        # Current search session state
        self.current_search_engine = 'google.com'
        self.search_attempts = 0
        self.max_search_attempts = 3
        
        logger.info("Search navigator initialized")
    
    async def execute_search_session(self, page: Page, keyword: str, 
                                   target_url: str) -> Dict[str, Any]:
        """Execute complete search session with target website visit"""
        session_result = {
            'keyword': keyword,
            'target_url': target_url,
            'search_engine': self.current_search_engine,
            'success': False,
            'pages_visited': 0,
            'time_on_target': 0,
            'search_attempts': 0,
            'errors': []
        }
        
        try:
            # Navigate to search engine
            await self._navigate_to_search_engine(page)
            
            # Perform search
            search_success = await self._perform_search(page, keyword)
            session_result['search_attempts'] = self.search_attempts
            
            if not search_success:
                session_result['errors'].append("Failed to perform search")
                return session_result
            
            # Find and click target website
            target_found = await self._find_and_visit_target(page, target_url)
            
            if target_found:
                session_result['success'] = True
                
                # Simulate browsing behavior on target site
                browsing_result = await self._browse_target_website(page, target_url)
                session_result.update(browsing_result)
            else:
                session_result['errors'].append("Target website not found in search results")
            
            return session_result
            
        except Exception as e:
            logger.error(f"Error in search session: {e}")
            session_result['errors'].append(str(e))
            return session_result
    
    async def _navigate_to_search_engine(self, page: Page):
        """Navigate to search engine homepage"""
        try:
            search_config = self.search_engines[self.current_search_engine]
            
            # Navigate to search engine
            await page.goto(search_config['url'], wait_until='networkidle')
            
            # Handle potential cookie/privacy banners
            await self._handle_cookie_banners(page)
            
            # Wait for search box to be ready
            await page.wait_for_selector(search_config['search_box'], timeout=10000)
            
            logger.debug(f"Navigated to {self.current_search_engine}")
            
        except Exception as e:
            logger.error(f"Failed to navigate to search engine: {e}")
            raise
    
    async def _handle_cookie_banners(self, page: Page):
        """Handle cookie consent and privacy banners"""
        try:
            # Common cookie banner selectors
            cookie_selectors = [
                'button:has-text("Accept")',
                'button:has-text("Accept all")',
                'button:has-text("I agree")',
                'button:has-text("OK")',
                'button[id*="accept"]',
                'button[class*="accept"]',
                '#L2AGLb',  # Google's "I agree" button
                '.QS5gu'   # Google's cookie banner
            ]
            
            for selector in cookie_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=2000)
                    if element:
                        await element.click()
                        await asyncio.sleep(1)
                        logger.debug("Handled cookie banner")
                        break
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"No cookie banner found or error handling: {e}")
    
    async def _perform_search(self, page: Page, keyword: str) -> bool:
        """Perform search with human-like behavior"""
        try:
            search_config = self.search_engines[self.current_search_engine]
            self.search_attempts += 1
            
            # Click on search box
            search_box = await page.wait_for_selector(search_config['search_box'])
            await self.behavior_simulator.simulate_link_clicking(page, search_config['search_box'])
            
            # Clear any existing text
            await page.keyboard.press('Control+a')
            await asyncio.sleep(random.uniform(0.1, 0.3))
            
            # Type search query with human-like behavior
            await self.behavior_simulator.simulate_typing(page, search_config['search_box'], keyword)
            
            # Wait before submitting
            await self.behavior_simulator.simulate_human_delay(0.5, 2.0)
            
            # Submit search (try Enter key first, then button)
            try:
                await page.keyboard.press('Enter')
            except:
                # Fallback to clicking search button
                await self.behavior_simulator.simulate_link_clicking(page, search_config['search_button'])
            
            # Wait for results to load
            await page.wait_for_selector(search_config['results_container'], timeout=15000)
            
            # Simulate reading search results
            await self.behavior_simulator.simulate_scrolling(page, random.uniform(0.3, 0.7))
            await self.behavior_simulator.simulate_human_delay(2, 5)
            
            logger.debug(f"Search performed for keyword: {keyword}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to perform search: {e}")
            
            # Try alternative search engine if available
            if self.search_attempts < self.max_search_attempts:
                await self._switch_search_engine(page)
                return await self._perform_search(page, keyword)
            
            return False
    
    async def _switch_search_engine(self, page: Page):
        """Switch to alternative search engine"""
        engines = list(self.search_engines.keys())
        current_index = engines.index(self.current_search_engine)
        next_index = (current_index + 1) % len(engines)
        self.current_search_engine = engines[next_index]
        
        logger.info(f"Switching to search engine: {self.current_search_engine}")
        await self._navigate_to_search_engine(page)
    
    async def _find_and_visit_target(self, page: Page, target_url: str) -> bool:
        """Find target website in search results and visit it"""
        try:
            search_config = self.search_engines[self.current_search_engine]
            target_domain = urlparse(target_url).netloc.lower()
            
            # Get all result links
            result_links = await page.query_selector_all(search_config['result_links'])
            
            if not result_links:
                logger.warning("No search result links found")
                return False
            
            # Look for target website in results
            target_links = []
            for link in result_links:
                try:
                    href = await link.get_attribute('href')
                    if href and target_domain in href.lower():
                        target_links.append(link)
                except:
                    continue
            
            if not target_links:
                # Try searching on next page
                return await self._search_next_page(page, target_url)
            
            # Select target link (prefer higher-ranked results)
            if len(target_links) > 1:
                # Weight selection towards top results
                weights = [1.0 / (i + 1) for i in range(len(target_links))]
                selected_link = random.choices(target_links, weights=weights)[0]
            else:
                selected_link = target_links[0]
            
            # Simulate human behavior before clicking
            await self._simulate_result_browsing(page, result_links, selected_link)
            
            # Click on target link
            await self.behavior_simulator.simulate_link_clicking(page, f'text="{await selected_link.text_content()}"')
            
            # Wait for target page to load
            await page.wait_for_load_state('networkidle', timeout=15000)
            
            # Verify we're on the target website
            current_url = page.url
            if target_domain in current_url.lower():
                logger.debug(f"Successfully visited target website: {current_url}")
                return True
            else:
                logger.warning(f"Landed on unexpected URL: {current_url}")
                return False
                
        except Exception as e:
            logger.error(f"Error finding/visiting target: {e}")
            return False
    
    async def _simulate_result_browsing(self, page: Page, all_links: List[ElementHandle], 
                                      target_link: ElementHandle):
        """Simulate human browsing behavior on search results"""
        try:
            # Scroll to make target link visible
            await target_link.scroll_into_view_if_needed()
            
            # Sometimes hover over other results first (curiosity behavior)
            if random.random() < 0.3 and len(all_links) > 1:
                other_links = [link for link in all_links if link != target_link]
                if other_links:
                    hover_link = random.choice(other_links[:5])  # Only top 5 results
                    try:
                        await hover_link.hover()
                        await asyncio.sleep(random.uniform(0.5, 2.0))
                    except:
                        pass
            
            # Pause before clicking target (reading/decision time)
            await self.behavior_simulator.simulate_human_delay(1, 4)
            
        except Exception as e:
            logger.debug(f"Error in result browsing simulation: {e}")
    
    async def _search_next_page(self, page: Page, target_url: str) -> bool:
        """Search for target on next page of results"""
        try:
            search_config = self.search_engines[self.current_search_engine]
            
            # Look for next page button
            next_button = await page.query_selector(search_config['next_page'])
            if not next_button:
                logger.debug("No next page button found")
                return False
            
            # Click next page
            await self.behavior_simulator.simulate_link_clicking(page, search_config['next_page'])
            await page.wait_for_load_state('networkidle')
            
            # Try to find target on this page
            return await self._find_and_visit_target(page, target_url)
            
        except Exception as e:
            logger.debug(f"Error searching next page: {e}")
            return False
    
    async def _browse_target_website(self, page: Page, target_url: str) -> Dict[str, Any]:
        """Browse target website with realistic behavior"""
        browsing_result = {
            'pages_visited': 1,
            'time_on_target': 0,
            'bounce': True,
            'internal_links_clicked': 0
        }
        
        try:
            start_time = asyncio.get_event_loop().time()
            
            # Initial page reading
            reading_time = random.randint(
                config.behavior.reading['min_time'],
                config.behavior.reading['max_time']
            )
            
            await self.behavior_simulator.simulate_reading_behavior(page, reading_time)
            
            # Visit additional pages
            pages_to_visit = random.randint(*config.behavior.reading['pages_to_visit'])
            
            for i in range(pages_to_visit):
                if await self._visit_internal_page(page, target_url):
                    browsing_result['pages_visited'] += 1
                    browsing_result['internal_links_clicked'] += 1
                    browsing_result['bounce'] = False
                    
                    # Read the new page
                    page_reading_time = random.randint(30, 120)
                    await self.behavior_simulator.simulate_reading_behavior(page, page_reading_time)
                else:
                    break
            
            # Calculate total time on target
            end_time = asyncio.get_event_loop().time()
            browsing_result['time_on_target'] = end_time - start_time
            
            logger.debug(f"Browsed target website: {browsing_result['pages_visited']} pages, "
                        f"{browsing_result['time_on_target']:.1f}s")
            
            return browsing_result
            
        except Exception as e:
            logger.error(f"Error browsing target website: {e}")
            return browsing_result
    
    async def _visit_internal_page(self, page: Page, target_url: str) -> bool:
        """Visit an internal page on the target website"""
        try:
            target_domain = urlparse(target_url).netloc
            
            # Find internal links
            internal_links = await page.query_selector_all(f'a[href*="{target_domain}"], a[href^="/"]')
            
            if not internal_links:
                logger.debug("No internal links found")
                return False
            
            # Filter out unwanted links (logout, admin, etc.)
            filtered_links = []
            unwanted_patterns = ['logout', 'admin', 'login', 'register', 'cart', 'checkout']
            
            for link in internal_links:
                try:
                    href = await link.get_attribute('href')
                    text = await link.text_content()
                    
                    if href and text:
                        # Skip unwanted links
                        if any(pattern in href.lower() or pattern in text.lower() 
                               for pattern in unwanted_patterns):
                            continue
                        
                        # Skip empty or very short text
                        if len(text.strip()) < 3:
                            continue
                            
                        filtered_links.append(link)
                        
                except:
                    continue
            
            if not filtered_links:
                return False
            
            # Select random internal link
            selected_link = random.choice(filtered_links)
            
            # Click the link
            await self.behavior_simulator.simulate_link_clicking(page, selected_link)
            await page.wait_for_load_state('networkidle', timeout=10000)
            
            # Verify we're still on the target domain
            current_url = page.url
            if target_domain in current_url:
                logger.debug(f"Visited internal page: {current_url}")
                return True
            else:
                logger.debug(f"Link led outside target domain: {current_url}")
                return False
                
        except Exception as e:
            logger.debug(f"Error visiting internal page: {e}")
            return False
    
    def get_search_statistics(self) -> Dict[str, Any]:
        """Get search session statistics"""
        return {
            'current_search_engine': self.current_search_engine,
            'search_attempts': self.search_attempts,
            'max_search_attempts': self.max_search_attempts,
            'available_search_engines': list(self.search_engines.keys())
        }
