#!/usr/bin/env python3
"""
Start High-Volume Traffic Generation NOW
Custom Configuration: 180-240s time on site, 70% mobile, USA only, 10% bounce rate
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime
import yaml

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("🚀 Starting High-Volume Traffic Generation")
print("=" * 50)
print("Configuration:")
print("  ⏱️  Time on Site: 180-240 seconds (3-4 minutes)")
print("  📱 Device Mix: 70% Mobile, 25% Desktop, 5% Tablet")
print("  🇺🇸 Geographic: USA Only (Different States)")
print("  📊 Bounce Rate: 10% (90% multi-page visits)")
print("  🎯 Target: 35,000 impressions + 55 clicks daily")
print("=" * 50)

async def start_traffic_generation():
    """Start traffic generation with custom configuration"""
    try:
        # Import the high-volume system
        from high_volume_main import HighVolumeTrafficSystem
        
        # Initialize with custom configuration
        config_file = "config_custom.yaml"
        
        # Check if custom config exists
        if not Path(config_file).exists():
            print(f"❌ Configuration file not found: {config_file}")
            print("💡 Using default high-volume configuration...")
            config_file = "config_high_volume.yaml"
        
        print(f"📋 Loading configuration: {config_file}")
        
        # Initialize system
        system = HighVolumeTrafficSystem(config_file)
        
        # Validate configuration
        print("🔍 Validating configuration...")
        validation = system.validate_high_volume_config()
        
        if not validation['valid']:
            print("❌ Configuration validation failed:")
            for error in validation['errors']:
                print(f"  - {error}")
            return False
        
        if validation['warnings']:
            print("⚠️  Configuration warnings:")
            for warning in validation['warnings']:
                print(f"  - {warning}")
        
        print("✅ Configuration validated successfully!")
        
        # Show system status
        print("\n📊 System Status:")
        status = system.get_system_status()
        print(f"  Brand: {status['high_volume_config']['brand_name']}")
        print(f"  Website: {status['high_volume_config']['target_url']}")
        print(f"  Daily Impressions: {status['high_volume_config']['daily_impressions']:,}")
        print(f"  Daily Clicks: {status['high_volume_config']['daily_clicks']}")
        print(f"  Target CTR: {status['high_volume_config']['target_ctr']:.4f}")
        
        # Start with a quick test first
        print("\n🧪 Starting Quick Test (1000 impressions, 2 clicks)...")
        test_result = await system.generate_high_volume_traffic(
            impressions=1000,
            clicks=2
        )
        
        if test_result['success']:
            print("✅ Quick test completed successfully!")
            print(f"  Impressions: {test_result['actual_impressions']}/1000")
            print(f"  Clicks: {test_result['actual_clicks']}/2")
            print(f"  CTR: {test_result['actual_ctr']:.4f}")
            print(f"  Success Rate: {test_result['success_rate']:.2%}")
            
            # Ask user if they want to proceed with full volume
            print("\n🚀 Quick test successful! Ready for full volume generation.")
            print("Full volume will generate 35,000 impressions and 55 clicks.")
            print("This will take 18-24 hours to complete naturally.")
            
            proceed = input("\nProceed with full volume generation? (y/N): ").strip().lower()
            
            if proceed == 'y':
                print("\n🚀 Starting Full Volume Traffic Generation...")
                print("Target: 35,000 impressions + 55 clicks")
                print("Expected Duration: 18-24 hours (distributed naturally)")
                print("Quality: 180-240s time on site, 10% bounce rate, 70% mobile")
                
                # Start full volume generation
                full_result = await system.generate_high_volume_traffic(
                    impressions=35000,
                    clicks=55
                )
                
                if full_result['success']:
                    print("\n🎉 FULL VOLUME GENERATION COMPLETED!")
                    print("=" * 45)
                    print(f"✅ Impressions Generated: {full_result['actual_impressions']:,}/35,000")
                    print(f"✅ Clicks Generated: {full_result['actual_clicks']}/55")
                    print(f"✅ Final CTR: {full_result['actual_ctr']:.4f}")
                    print(f"✅ Success Rate: {full_result['success_rate']:.2%}")
                    print(f"✅ Total Duration: {full_result['execution_time']/3600:.1f} hours")
                    print("=" * 45)
                    
                    # Get final analytics
                    ctr_metrics = system.ic_manager.get_ctr_metrics()
                    print("\n📊 Quality Metrics:")
                    print(f"  Average Time on Site: {ctr_metrics['quality_metrics']['avg_time_on_site']:.1f}s")
                    print(f"  Bounce Rate: {ctr_metrics['quality_metrics']['bounce_rate']:.1%}")
                    print(f"  Average Time on SERP: {ctr_metrics['quality_metrics']['avg_time_on_serp']:.1f}s")
                    
                    return True
                else:
                    print(f"\n❌ Full volume generation failed: {full_result.get('error', 'Unknown error')}")
                    return False
            else:
                print("❌ Full volume generation cancelled by user")
                return False
        else:
            print(f"❌ Quick test failed: {test_result.get('error', 'Unknown error')}")
            print("💡 Please check your configuration and proxy settings")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Please run: python install.py")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def start_daily_schedule():
    """Start daily scheduled traffic generation"""
    try:
        from high_volume_main import HighVolumeTrafficSystem
        
        print("\n📅 Starting Daily Scheduled Traffic Generation...")
        
        system = HighVolumeTrafficSystem("config_custom.yaml")
        
        result = await system.schedule_daily_high_volume()
        
        if result.get('success', True):
            print("✅ Daily schedule completed!")
            print(f"  Status: {result['plan_status']}")
            print(f"  Impressions: {result['impressions_generated']:,}")
            print(f"  Clicks: {result['clicks_generated']}")
            print(f"  Final CTR: {result['final_ctr']:.4f}")
            print(f"  Success Rate: {result['success_rate']:.2%}")
            return True
        else:
            print(f"❌ Daily schedule failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Daily schedule error: {e}")
        return False

def show_menu():
    """Show traffic generation menu"""
    print("\n🎯 Traffic Generation Options:")
    print("-" * 30)
    print("1. Quick Test (1000 impressions, 2 clicks)")
    print("2. Full Volume (35k impressions, 55 clicks)")
    print("3. Daily Schedule (distributed over 24 hours)")
    print("4. Exit")
    print()
    
    choice = input("Select option (1-4): ").strip()
    return choice

async def main():
    """Main function"""
    try:
        # Check if we should run automatically
        if len(sys.argv) > 1:
            if sys.argv[1] == "--auto":
                return await start_traffic_generation()
            elif sys.argv[1] == "--schedule":
                return await start_daily_schedule()
        
        # Interactive mode
        while True:
            choice = show_menu()
            
            if choice == '1':
                # Quick test only
                from high_volume_main import HighVolumeTrafficSystem
                system = HighVolumeTrafficSystem("config_custom.yaml")
                result = await system.generate_high_volume_traffic(impressions=1000, clicks=2)
                
                if result['success']:
                    print("✅ Quick test completed!")
                    print(f"  Impressions: {result['actual_impressions']}/1000")
                    print(f"  Clicks: {result['actual_clicks']}/2")
                    print(f"  CTR: {result['actual_ctr']:.4f}")
                else:
                    print(f"❌ Quick test failed: {result.get('error')}")
                    
            elif choice == '2':
                # Full volume
                success = await start_traffic_generation()
                if success:
                    print("🎉 Traffic generation completed successfully!")
                    break
                    
            elif choice == '3':
                # Daily schedule
                success = await start_daily_schedule()
                if success:
                    print("🎉 Daily schedule completed successfully!")
                    break
                    
            elif choice == '4':
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please select 1-4.")
            
            input("\nPress Enter to continue...")
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n👋 Traffic generation stopped by user")
        return False
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 High-Volume Traffic Generator Starting...")
    
    # Check for environment variables
    if not os.getenv('PROXY_API_KEY') or os.getenv('PROXY_API_KEY') == 'your_proxy_api_key_here':
        print("⚠️  Warning: PROXY_API_KEY not configured in .env file")
        print("💡 Please set your proxy API key for best results")
    
    # Run the traffic generation
    success = asyncio.run(main())
    
    if success:
        print("\n🎉 Traffic generation completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Traffic generation failed")
        sys.exit(1)
