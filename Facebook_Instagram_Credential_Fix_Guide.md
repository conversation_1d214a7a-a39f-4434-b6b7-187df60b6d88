# 🔧 Facebook & Instagram Credential Issues - Complete Fix Guide

## 🚨 **Common Credential Errors & Solutions**

### **Error 1: "Invalid Access Token"**
**Symptoms**: Facebook/Instagram nodes fail with authentication errors
**Cause**: Expired or incorrect access token

**Solution:**
1. **Go to Facebook Developers Console**
   - Visit: `developers.facebook.com`
   - Select your app
   - Go to "Tools & Support" → "Access Token Tool"

2. **Generate New Access Token**
   - Click "Generate Access Token"
   - Select all required permissions:
     - `pages_manage_posts`
     - `pages_read_engagement`
     - `instagram_basic`
     - `instagram_content_publish`
     - `publish_to_groups`

3. **Update n8n Credential**
   - Copy the new access token
   - In n8n, edit your Facebook Graph API credential
   - Paste the new token
   - Test the connection

### **Error 2: "Insufficient Permissions"**
**Symptoms**: Posts fail with permission denied errors
**Cause**: Missing required permissions

**Solution:**
1. **Check App Permissions**
   - In Facebook Developers Console
   - Go to "App Review" → "Permissions and Features"
   - Ensure these are approved:
     - `pages_manage_posts` (Standard Access)
     - `instagram_content_publish` (Standard Access)

2. **Request Advanced Permissions**
   - Click "Request Advanced Access"
   - Fill out the use case form
   - Wait for Facebook approval (1-7 days)

### **Error 3: "Page Not Found" or "Instagram Account Not Connected"**
**Symptoms**: Can't post to specific pages/accounts
**Cause**: Page/Instagram account not properly connected

**Solution:**
1. **Connect Facebook Page**
   - Go to Facebook Business Manager
   - Add your Facebook page
   - Ensure you have admin access

2. **Connect Instagram Business Account**
   - Go to Instagram Settings
   - Switch to Business Account
   - Connect to your Facebook page
   - Verify connection in Facebook Business Manager

### **Error 4: "App Not Approved for Public Use"**
**Symptoms**: Works in development but fails in production
**Cause**: App still in development mode

**Solution:**
1. **Submit App for Review**
   - Complete App Review process
   - Provide detailed use case
   - Include privacy policy and terms of service
   - Wait for Facebook approval

2. **Use Test Users (Temporary)**
   - Add test users in Facebook Developers Console
   - Use test accounts for posting
   - Limited to 5 test users

## 🛠️ **Step-by-Step Credential Setup (Fixed Version)**

### **Step 1: Create Facebook App (Correct Way)**

1. **Go to Facebook Developers**
   - URL: `developers.facebook.com`
   - Click "My Apps" → "Create App"

2. **Choose App Type**
   - Select "Business" (not Consumer)
   - App Name: "Your Company Social Media Manager"
   - Contact Email: Your business email

3. **Add Products**
   - Click "Add Product"
   - Add "Facebook Login"
   - Add "Instagram Basic Display" (if using Instagram)

4. **Configure Facebook Login**
   - Go to Facebook Login → Settings
   - Add Valid OAuth Redirect URIs:
     - `https://your-n8n-instance.com/rest/oauth2-credential/callback`
     - `http://localhost:5678/rest/oauth2-credential/callback` (for local testing)

### **Step 2: Get Correct Credentials**

1. **App ID and App Secret**
   - Go to Settings → Basic
   - Copy "App ID": `****************`
   - Copy "App Secret": `abcd1234efgh5678ijkl9012mnop3456`

2. **Generate Access Token**
   - Go to Tools → Access Token Tool
   - Generate User Access Token
   - Select all required permissions
   - Copy the token: `EAABwzLixnjYBAO...`

### **Step 3: Configure n8n Credential (Fixed)**

1. **Create Facebook Graph API Credential**
   - In n8n: Credentials → Add Credential
   - Search: "Facebook Graph API"
   - Fill in:
     - **Client ID**: Your App ID
     - **Client Secret**: Your App Secret
     - **Access Token**: Your generated token

2. **Test Connection**
   - Click "Test"
   - Should return: "Connection successful"
   - If failed, check permissions and token

### **Step 4: Instagram-Specific Setup**

1. **Connect Instagram Business Account**
   - Instagram app → Settings → Account
   - Switch to Professional Account → Business
   - Connect to Facebook Page

2. **Get Instagram Business Account ID**
   - Use Facebook Graph API Explorer
   - Query: `me/accounts`
   - Find your page, then query: `{page-id}?fields=instagram_business_account`
   - Copy the Instagram Business Account ID

3. **Update n8n Instagram Nodes**
   - Use Instagram Business Account ID in API calls
   - Endpoint: `https://graph.facebook.com/v18.0/{instagram-account-id}/media`

## 🔍 **Testing Your Setup**

### **Test 1: Basic API Connection**
```bash
curl -X GET "https://graph.facebook.com/v18.0/me?access_token=YOUR_ACCESS_TOKEN"
```
**Expected**: Your user information

### **Test 2: Page Access**
```bash
curl -X GET "https://graph.facebook.com/v18.0/me/accounts?access_token=YOUR_ACCESS_TOKEN"
```
**Expected**: List of pages you manage

### **Test 3: Instagram Account**
```bash
curl -X GET "https://graph.facebook.com/v18.0/{PAGE_ID}?fields=instagram_business_account&access_token=YOUR_ACCESS_TOKEN"
```
**Expected**: Instagram business account ID

### **Test 4: Post to Facebook**
```bash
curl -X POST "https://graph.facebook.com/v18.0/{PAGE_ID}/photos" \
  -F "url=https://example.com/image.jpg" \
  -F "caption=Test post from API" \
  -F "access_token=YOUR_ACCESS_TOKEN"
```
**Expected**: Post ID and success message

## 🚨 **Common Mistakes to Avoid**

### **Mistake 1: Using Personal Access Token**
- ❌ **Wrong**: Personal user token
- ✅ **Correct**: Page access token with proper permissions

### **Mistake 2: Wrong API Endpoints**
- ❌ **Wrong**: `/me/feed` for page posting
- ✅ **Correct**: `/{page-id}/photos` for image posts

### **Mistake 3: Missing Permissions**
- ❌ **Wrong**: Basic permissions only
- ✅ **Correct**: All required permissions approved

### **Mistake 4: Development Mode**
- ❌ **Wrong**: App in development mode for production
- ✅ **Correct**: App reviewed and approved for public use

## 🔄 **Updated Workflow Configuration**

### **Facebook Text + Image Post (Fixed)**
```json
{
  "url": "https://graph.facebook.com/v18.0/{PAGE_ID}/photos",
  "method": "POST",
  "body": {
    "url": "IMAGE_URL",
    "caption": "POST_TEXT",
    "access_token": "PAGE_ACCESS_TOKEN"
  }
}
```

### **Instagram Text + Image Post (Fixed)**
```json
{
  "step1": {
    "url": "https://graph.facebook.com/v18.0/{INSTAGRAM_ACCOUNT_ID}/media",
    "body": {
      "image_url": "IMAGE_URL",
      "caption": "POST_TEXT",
      "access_token": "ACCESS_TOKEN"
    }
  },
  "step2": {
    "url": "https://graph.facebook.com/v18.0/{INSTAGRAM_ACCOUNT_ID}/media_publish",
    "body": {
      "creation_id": "MEDIA_CONTAINER_ID",
      "access_token": "ACCESS_TOKEN"
    }
  }
}
```

## 🎯 **Success Checklist**

- [ ] Facebook app created with correct type (Business)
- [ ] All required permissions requested and approved
- [ ] Valid OAuth redirect URIs configured
- [ ] Page access token generated (not user token)
- [ ] Instagram business account connected to Facebook page
- [ ] Instagram business account ID obtained
- [ ] n8n credentials configured with correct tokens
- [ ] API endpoints updated to use correct URLs
- [ ] Test posts successful on both platforms
- [ ] Error handling implemented for rate limits

## 🆘 **Still Having Issues?**

### **Debug Steps:**
1. **Check Facebook App Status**
   - Ensure app is not restricted
   - Verify all permissions are granted
   - Check for any policy violations

2. **Validate Access Tokens**
   - Use Facebook's Access Token Debugger
   - URL: `developers.facebook.com/tools/debug/accesstoken/`
   - Check token expiration and permissions

3. **Review API Responses**
   - Enable detailed logging in n8n
   - Check exact error messages
   - Look for rate limiting or permission issues

4. **Contact Support**
   - Facebook Developer Support
   - n8n Community Forums
   - Check Facebook Developer Documentation

**Your Facebook and Instagram posting should now work perfectly with text + images! 🎉**
