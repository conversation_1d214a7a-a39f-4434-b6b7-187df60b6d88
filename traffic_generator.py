"""
Advanced Organic Traffic Generator

This is the main traffic generation engine that orchestrates all components
to create realistic, human-like traffic to target websites.
"""

import asyncio
import random
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from loguru import logger

# Import all components
from browser_manager import BrowserManager
from proxy_manager import Proxy<PERSON>anager
from fingerprint_generator import FingerprintGenerator
from behavior_simulator import BehaviorSimulator
from search_navigator import SearchNavigator
from analytics_logger import AnalyticsLogger
from config_manager import config

class AdvancedTrafficGenerator:
    """Advanced traffic generator with comprehensive anti-detection capabilities"""

    def __init__(self):
        """Initialize advanced traffic generator"""
        self.config = config
        self.target_url = config.target.url
        self.target_domain = config.target.domain

        # Initialize components
        self.proxy_manager = ProxyManager()
        self.fingerprint_generator = FingerprintGenerator()
        self.behavior_simulator = BehaviorSimulator()
        self.search_navigator = SearchNavigator()
        self.analytics_logger = AnalyticsLogger()

        # Session tracking
        self.active_sessions = {}

        logger.info("Advanced traffic generator initialized")

    async def execute_session(self, keyword: str, region: str = "US",
                            device_type: str = "desktop",
                            proxy_type: str = "datacenter") -> Dict[str, Any]:
        """Execute a complete traffic generation session"""
        session_id = str(uuid.uuid4())

        try:
            # Start session tracking
            session_metrics = self.analytics_logger.start_session(
                session_id=session_id,
                keyword=keyword,
                target_url=self.target_url,
                proxy_region=region,
                user_agent="",  # Will be set later
                device_type=device_type
            )

            logger.info(f"Starting session {session_id} for keyword: {keyword}")

            # Get proxy for this session
            proxy = await self.proxy_manager.get_proxy(region=region, proxy_type=proxy_type)
            if not proxy:
                raise Exception("No available proxy found")

            # Generate comprehensive fingerprint
            fingerprint = self.fingerprint_generator.generate_comprehensive_fingerprint(
                region=region,
                device_preference=device_type
            )

            # Update session with actual user agent
            session_metrics.user_agent = fingerprint['user_agent']

            # Execute session with browser automation
            result = await self._execute_browser_session(
                session_id, keyword, proxy, fingerprint
            )

            # End session tracking
            self.analytics_logger.end_session(
                session_id=session_id,
                success=result['success'],
                pages_visited=result.get('pages_visited', 0),
                ip_address=proxy.host
            )

            # Report proxy performance
            if result['success']:
                self.proxy_manager.report_proxy_success(proxy)
            else:
                self.proxy_manager.report_proxy_failure(proxy, result.get('error'))

            logger.info(f"Session {session_id} completed: {result['success']}")
            return result

        except Exception as e:
            logger.error(f"Session {session_id} failed: {e}")

            # Log error and end session
            self.analytics_logger.log_error(session_id, keyword, "session_error", str(e))
            self.analytics_logger.end_session(session_id, success=False, error_message=str(e))

            # Report proxy failure if proxy was obtained
            if 'proxy' in locals():
                self.proxy_manager.report_proxy_failure(proxy, str(e))

            return {
                'success': False,
                'error': str(e),
                'session_id': session_id,
                'keyword': keyword
            }

    async def _execute_browser_session(self, session_id: str, keyword: str,
                                     proxy: Any, fingerprint: Dict[str, Any]) -> Dict[str, Any]:
        """Execute browser automation session"""
        result = {
            'success': False,
            'pages_visited': 0,
            'time_on_site': 0,
            'search_engine': 'google.com',
            'error': None
        }

        try:
            # Initialize browser manager
            async with BrowserManager() as browser_manager:
                # Create stealth browser with proxy
                proxy_config = {
                    'protocol': proxy.protocol,
                    'host': proxy.host,
                    'port': proxy.port,
                    'username': proxy.username,
                    'password': proxy.password
                }

                browser = await browser_manager.create_stealth_browser(
                    proxy_config=proxy_config,
                    headless=True
                )

                # Create stealth context with fingerprint
                context = await browser_manager.create_stealth_context(
                    fingerprint=fingerprint,
                    locale=fingerprint['locale']
                )

                # Create stealth page
                page = await browser_manager.create_stealth_page(context)

                # Execute search and navigation
                search_result = await self.search_navigator.execute_search_session(
                    page=page,
                    keyword=keyword,
                    target_url=self.target_url
                )

                # Update result with search outcome
                result.update(search_result)

                return result

        except Exception as e:
            result['error'] = str(e)
            logger.error(f"Browser session error: {e}")
            return result

    async def generate_traffic_batch(self, sessions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate traffic for a batch of sessions"""
        results = []

        try:
            # Create semaphore for concurrency control
            max_concurrent = config.scheduling.rate_limiting['max_concurrent']
            semaphore = asyncio.Semaphore(max_concurrent)

            # Create tasks for all sessions
            tasks = []
            for session_config in sessions:
                task = asyncio.create_task(
                    self._execute_session_with_semaphore(session_config, semaphore)
                )
                tasks.append(task)

            # Execute all sessions
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append({
                        'success': False,
                        'error': str(result),
                        'session_config': sessions[i]
                    })
                else:
                    processed_results.append(result)

            return processed_results

        except Exception as e:
            logger.error(f"Error in traffic batch generation: {e}")
            return results

    async def _execute_session_with_semaphore(self, session_config: Dict[str, Any],
                                            semaphore: asyncio.Semaphore) -> Dict[str, Any]:
        """Execute session with concurrency control"""
        async with semaphore:
            return await self.execute_session(**session_config)

    def get_generator_statistics(self) -> Dict[str, Any]:
        """Get traffic generator statistics"""
        return {
            'active_sessions': len(self.active_sessions),
            'proxy_stats': self.proxy_manager.get_proxy_statistics(),
            'search_stats': self.search_navigator.get_search_statistics(),
            'analytics_summary': self.analytics_logger.generate_daily_summary()
        }

    async def cleanup(self):
        """Cleanup generator resources"""
        try:
            # Wait for active sessions to complete
            if self.active_sessions:
                logger.info(f"Waiting for {len(self.active_sessions)} active sessions to complete")
                await asyncio.sleep(5)  # Give sessions time to complete

            logger.info("Traffic generator cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Legacy compatibility class
class TrafficGenerator:
    """Legacy traffic generator for backward compatibility"""

    def __init__(self, config_dict):
        """Initialize with legacy config format"""
        self.advanced_generator = AdvancedTrafficGenerator()
        self.config_dict = config_dict

    def generate_traffic(self, num_sessions: int = None):
        """Generate traffic using legacy interface"""
        if num_sessions is None:
            num_sessions = random.randint(10, 40)

        # Convert to async execution
        asyncio.run(self._generate_traffic_async(num_sessions))

    async def _generate_traffic_async(self, num_sessions: int):
        """Async traffic generation"""
        sessions = []
        keywords = self.config_dict.get('keywords', ['test keyword'])

        for _ in range(num_sessions):
            session_config = {
                'keyword': random.choice(keywords),
                'region': 'US',
                'device_type': random.choice(['desktop', 'mobile']),
                'proxy_type': 'datacenter'
            }
            sessions.append(session_config)

        results = await self.advanced_generator.generate_traffic_batch(sessions)

        # Log results
        successful = sum(1 for r in results if r.get('success', False))
        failed = len(results) - successful

        logger.info(f"Legacy traffic generation completed: {successful} successful, {failed} failed")
        return successful, failed