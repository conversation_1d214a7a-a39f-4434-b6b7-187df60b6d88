"""
Comprehensive Error Handling & Recovery System

This module implements robust error handling, retry mechanisms,
session cleanup, and failure recovery protocols for the traffic generation system.
"""

import asyncio
import traceback
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from loguru import logger
from config_manager import config

class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """Error categories for classification"""
    NETWORK = "network"
    PROXY = "proxy"
    BROWSER = "browser"
    SEARCH = "search"
    TARGET_SITE = "target_site"
    FINGERPRINT = "fingerprint"
    SYSTEM = "system"
    CONFIGURATION = "configuration"

@dataclass
class ErrorInfo:
    """Comprehensive error information"""
    error_id: str
    timestamp: datetime
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    exception: Optional[Exception] = None
    stack_trace: Optional[str] = None
    session_id: Optional[str] = None
    keyword: Optional[str] = None
    proxy_info: Optional[Dict] = None
    retry_count: int = 0
    max_retries: int = 3
    recovery_attempted: bool = False
    resolved: bool = False

@dataclass
class RecoveryStrategy:
    """Recovery strategy configuration"""
    name: str
    applicable_categories: List[ErrorCategory]
    applicable_severities: List[ErrorSeverity]
    max_attempts: int
    delay_between_attempts: float
    recovery_function: Callable
    fallback_strategy: Optional[str] = None

class ErrorHandler:
    """Comprehensive error handling and recovery system"""
    
    def __init__(self):
        """Initialize error handler"""
        self.config = config.error_handling
        self.error_history: List[ErrorInfo] = []
        self.recovery_strategies: Dict[str, RecoveryStrategy] = {}
        self.failure_thresholds = self.config.thresholds
        self.emergency_stop_threshold = config.compliance.safety['emergency_stop_threshold']
        
        # Statistics
        self.stats = {
            'total_errors': 0,
            'errors_by_category': {},
            'errors_by_severity': {},
            'successful_recoveries': 0,
            'failed_recoveries': 0,
            'emergency_stops': 0
        }
        
        # Initialize recovery strategies
        self._initialize_recovery_strategies()
        
        logger.info("Error handler initialized")
    
    def _initialize_recovery_strategies(self):
        """Initialize recovery strategies"""
        
        # Proxy recovery strategy
        self.recovery_strategies['proxy_rotation'] = RecoveryStrategy(
            name="proxy_rotation",
            applicable_categories=[ErrorCategory.PROXY, ErrorCategory.NETWORK],
            applicable_severities=[ErrorSeverity.LOW, ErrorSeverity.MEDIUM],
            max_attempts=3,
            delay_between_attempts=5.0,
            recovery_function=self._recover_proxy_issues,
            fallback_strategy="emergency_proxy_refresh"
        )
        
        # Browser recovery strategy
        self.recovery_strategies['browser_restart'] = RecoveryStrategy(
            name="browser_restart",
            applicable_categories=[ErrorCategory.BROWSER],
            applicable_severities=[ErrorSeverity.MEDIUM, ErrorSeverity.HIGH],
            max_attempts=2,
            delay_between_attempts=10.0,
            recovery_function=self._recover_browser_issues,
            fallback_strategy="session_abort"
        )
        
        # Search engine fallback
        self.recovery_strategies['search_engine_fallback'] = RecoveryStrategy(
            name="search_engine_fallback",
            applicable_categories=[ErrorCategory.SEARCH],
            applicable_severities=[ErrorSeverity.LOW, ErrorSeverity.MEDIUM],
            max_attempts=2,
            delay_between_attempts=3.0,
            recovery_function=self._recover_search_issues,
            fallback_strategy="session_abort"
        )
        
        # Fingerprint regeneration
        self.recovery_strategies['fingerprint_regeneration'] = RecoveryStrategy(
            name="fingerprint_regeneration",
            applicable_categories=[ErrorCategory.FINGERPRINT],
            applicable_severities=[ErrorSeverity.LOW, ErrorSeverity.MEDIUM],
            max_attempts=2,
            delay_between_attempts=1.0,
            recovery_function=self._recover_fingerprint_issues,
            fallback_strategy="session_abort"
        )
        
        # Emergency proxy refresh
        self.recovery_strategies['emergency_proxy_refresh'] = RecoveryStrategy(
            name="emergency_proxy_refresh",
            applicable_categories=[ErrorCategory.PROXY],
            applicable_severities=[ErrorSeverity.HIGH, ErrorSeverity.CRITICAL],
            max_attempts=1,
            delay_between_attempts=30.0,
            recovery_function=self._emergency_proxy_refresh,
            fallback_strategy="system_pause"
        )
        
        # System pause (last resort)
        self.recovery_strategies['system_pause'] = RecoveryStrategy(
            name="system_pause",
            applicable_categories=list(ErrorCategory),
            applicable_severities=[ErrorSeverity.CRITICAL],
            max_attempts=1,
            delay_between_attempts=300.0,  # 5 minutes
            recovery_function=self._system_pause_recovery,
            fallback_strategy=None
        )
    
    async def handle_error(self, exception: Exception, context: Dict[str, Any] = None) -> ErrorInfo:
        """Handle an error with comprehensive analysis and recovery"""
        try:
            # Create error info
            error_info = self._analyze_error(exception, context)
            
            # Log error
            self._log_error(error_info)
            
            # Update statistics
            self._update_error_statistics(error_info)
            
            # Check for emergency stop conditions
            if self._should_emergency_stop():
                await self._trigger_emergency_stop()
                error_info.severity = ErrorSeverity.CRITICAL
                return error_info
            
            # Attempt recovery
            if error_info.severity != ErrorSeverity.CRITICAL:
                recovery_success = await self._attempt_recovery(error_info)
                if recovery_success:
                    error_info.resolved = True
                    self.stats['successful_recoveries'] += 1
                else:
                    self.stats['failed_recoveries'] += 1
            
            # Store error in history
            self.error_history.append(error_info)
            
            return error_info
            
        except Exception as e:
            logger.critical(f"Error in error handler: {e}")
            # Return basic error info as fallback
            return ErrorInfo(
                error_id=f"error_{datetime.now().timestamp()}",
                timestamp=datetime.now(),
                category=ErrorCategory.SYSTEM,
                severity=ErrorSeverity.CRITICAL,
                message=str(exception),
                exception=exception
            )
    
    def _analyze_error(self, exception: Exception, context: Dict[str, Any] = None) -> ErrorInfo:
        """Analyze error and determine category and severity"""
        error_id = f"error_{datetime.now().timestamp()}"
        timestamp = datetime.now()
        
        # Determine category based on exception type and context
        category = self._categorize_error(exception, context)
        
        # Determine severity
        severity = self._assess_severity(exception, category, context)
        
        # Extract context information
        session_id = context.get('session_id') if context else None
        keyword = context.get('keyword') if context else None
        proxy_info = context.get('proxy_info') if context else None
        
        return ErrorInfo(
            error_id=error_id,
            timestamp=timestamp,
            category=category,
            severity=severity,
            message=str(exception),
            exception=exception,
            stack_trace=traceback.format_exc(),
            session_id=session_id,
            keyword=keyword,
            proxy_info=proxy_info,
            max_retries=self.config.max_retries
        )
    
    def _categorize_error(self, exception: Exception, context: Dict[str, Any] = None) -> ErrorCategory:
        """Categorize error based on exception type and context"""
        exception_name = type(exception).__name__.lower()
        error_message = str(exception).lower()
        
        # Network-related errors
        if any(keyword in exception_name or keyword in error_message for keyword in 
               ['timeout', 'connection', 'network', 'dns', 'socket']):
            return ErrorCategory.NETWORK
        
        # Proxy-related errors
        if any(keyword in exception_name or keyword in error_message for keyword in 
               ['proxy', 'authentication', 'forbidden', '407', '403']):
            return ErrorCategory.PROXY
        
        # Browser-related errors
        if any(keyword in exception_name or keyword in error_message for keyword in 
               ['browser', 'playwright', 'chromium', 'page', 'context']):
            return ErrorCategory.BROWSER
        
        # Search-related errors
        if any(keyword in exception_name or keyword in error_message for keyword in 
               ['search', 'selector', 'element', 'not found']):
            return ErrorCategory.SEARCH
        
        # Target site errors
        if any(keyword in exception_name or keyword in error_message for keyword in 
               ['target', 'navigation', '404', '500', 'server error']):
            return ErrorCategory.TARGET_SITE
        
        # Configuration errors
        if any(keyword in exception_name or keyword in error_message for keyword in 
               ['config', 'setting', 'parameter', 'missing']):
            return ErrorCategory.CONFIGURATION
        
        # Default to system error
        return ErrorCategory.SYSTEM
    
    def _assess_severity(self, exception: Exception, category: ErrorCategory, 
                        context: Dict[str, Any] = None) -> ErrorSeverity:
        """Assess error severity"""
        exception_name = type(exception).__name__.lower()
        
        # Critical errors
        if any(keyword in exception_name for keyword in 
               ['critical', 'fatal', 'system', 'memory', 'disk']):
            return ErrorSeverity.CRITICAL
        
        # High severity errors
        if category in [ErrorCategory.SYSTEM, ErrorCategory.CONFIGURATION]:
            return ErrorSeverity.HIGH
        
        if any(keyword in exception_name for keyword in 
               ['permission', 'access', 'authentication']):
            return ErrorSeverity.HIGH
        
        # Medium severity errors
        if category in [ErrorCategory.BROWSER, ErrorCategory.PROXY]:
            return ErrorSeverity.MEDIUM
        
        # Low severity errors (recoverable)
        if category in [ErrorCategory.NETWORK, ErrorCategory.SEARCH, ErrorCategory.TARGET_SITE]:
            return ErrorSeverity.LOW
        
        return ErrorSeverity.MEDIUM  # Default
    
    def _log_error(self, error_info: ErrorInfo):
        """Log error with appropriate level"""
        log_message = f"[{error_info.category.value}] {error_info.message}"
        
        if error_info.session_id:
            log_message += f" (Session: {error_info.session_id})"
        
        if error_info.keyword:
            log_message += f" (Keyword: {error_info.keyword})"
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
        elif error_info.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
    
    def _update_error_statistics(self, error_info: ErrorInfo):
        """Update error statistics"""
        self.stats['total_errors'] += 1
        
        # Update category statistics
        category_key = error_info.category.value
        self.stats['errors_by_category'][category_key] = \
            self.stats['errors_by_category'].get(category_key, 0) + 1
        
        # Update severity statistics
        severity_key = error_info.severity.value
        self.stats['errors_by_severity'][severity_key] = \
            self.stats['errors_by_severity'].get(severity_key, 0) + 1
    
    def _should_emergency_stop(self) -> bool:
        """Check if emergency stop should be triggered"""
        if len(self.error_history) < 10:  # Need minimum sample size
            return False
        
        # Check recent error rate
        recent_errors = [e for e in self.error_history[-20:] 
                        if (datetime.now() - e.timestamp).total_seconds() < 300]  # Last 5 minutes
        
        if len(recent_errors) >= 10:  # Too many errors in short time
            return True
        
        # Check critical error rate
        critical_errors = [e for e in self.error_history[-10:] 
                          if e.severity == ErrorSeverity.CRITICAL]
        
        if len(critical_errors) >= 3:  # Too many critical errors
            return True
        
        # Check overall failure rate
        recent_total = len(self.error_history[-50:])
        if recent_total > 0:
            failure_rate = len([e for e in self.error_history[-50:] if not e.resolved]) / recent_total
            if failure_rate >= self.emergency_stop_threshold:
                return True
        
        return False
    
    async def _trigger_emergency_stop(self):
        """Trigger emergency stop"""
        logger.critical("EMERGENCY STOP TRIGGERED - High error rate detected")
        self.stats['emergency_stops'] += 1
        
        # Implement emergency stop logic here
        # This could include:
        # - Stopping all active sessions
        # - Sending alerts
        # - Saving current state
        # - Initiating recovery procedures
        
        # For now, just log and pause
        await asyncio.sleep(300)  # 5-minute pause
    
    async def _attempt_recovery(self, error_info: ErrorInfo) -> bool:
        """Attempt to recover from error"""
        try:
            # Find applicable recovery strategies
            applicable_strategies = [
                strategy for strategy in self.recovery_strategies.values()
                if (error_info.category in strategy.applicable_categories and
                    error_info.severity in strategy.applicable_severities)
            ]
            
            if not applicable_strategies:
                logger.debug(f"No recovery strategy found for {error_info.category.value} error")
                return False
            
            # Try each strategy
            for strategy in applicable_strategies:
                if error_info.retry_count < strategy.max_attempts:
                    logger.info(f"Attempting recovery with strategy: {strategy.name}")
                    
                    # Wait before retry
                    if error_info.retry_count > 0:
                        await asyncio.sleep(strategy.delay_between_attempts)
                    
                    # Attempt recovery
                    success = await strategy.recovery_function(error_info)
                    error_info.retry_count += 1
                    error_info.recovery_attempted = True
                    
                    if success:
                        logger.info(f"Recovery successful with strategy: {strategy.name}")
                        return True
                    else:
                        logger.warning(f"Recovery failed with strategy: {strategy.name}")
                
                # Try fallback strategy if available
                if strategy.fallback_strategy and strategy.fallback_strategy in self.recovery_strategies:
                    fallback = self.recovery_strategies[strategy.fallback_strategy]
                    logger.info(f"Trying fallback strategy: {fallback.name}")
                    success = await fallback.recovery_function(error_info)
                    if success:
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error during recovery attempt: {e}")
            return False
    
    # Recovery strategy implementations
    async def _recover_proxy_issues(self, error_info: ErrorInfo) -> bool:
        """Recover from proxy-related issues"""
        try:
            # Import here to avoid circular imports
            from proxy_manager import ProxyManager
            
            proxy_manager = ProxyManager()
            
            # Force proxy refresh
            await proxy_manager.refresh_proxies(force=True)
            
            # Get new proxy
            new_proxy = await proxy_manager.get_proxy(exclude_failed=True)
            
            return new_proxy is not None
            
        except Exception as e:
            logger.error(f"Proxy recovery failed: {e}")
            return False
    
    async def _recover_browser_issues(self, error_info: ErrorInfo) -> bool:
        """Recover from browser-related issues"""
        try:
            # Browser issues typically require session restart
            logger.info("Browser recovery: session restart required")
            return False  # Indicate session should be restarted
            
        except Exception as e:
            logger.error(f"Browser recovery failed: {e}")
            return False
    
    async def _recover_search_issues(self, error_info: ErrorInfo) -> bool:
        """Recover from search-related issues"""
        try:
            # Search issues can often be resolved by switching search engines
            logger.info("Search recovery: switching search engine")
            return True  # Let search navigator handle the switch
            
        except Exception as e:
            logger.error(f"Search recovery failed: {e}")
            return False
    
    async def _recover_fingerprint_issues(self, error_info: ErrorInfo) -> bool:
        """Recover from fingerprint-related issues"""
        try:
            # Regenerate fingerprint
            logger.info("Fingerprint recovery: regenerating fingerprint")
            return True  # New fingerprint will be generated
            
        except Exception as e:
            logger.error(f"Fingerprint recovery failed: {e}")
            return False
    
    async def _emergency_proxy_refresh(self, error_info: ErrorInfo) -> bool:
        """Emergency proxy refresh"""
        try:
            logger.warning("Emergency proxy refresh initiated")
            # Implement emergency proxy refresh logic
            await asyncio.sleep(30)  # Simulate refresh time
            return True
            
        except Exception as e:
            logger.error(f"Emergency proxy refresh failed: {e}")
            return False
    
    async def _system_pause_recovery(self, error_info: ErrorInfo) -> bool:
        """System pause recovery (last resort)"""
        try:
            logger.critical("System pause recovery initiated")
            await asyncio.sleep(300)  # 5-minute pause
            return True
            
        except Exception as e:
            logger.error(f"System pause recovery failed: {e}")
            return False
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get comprehensive error statistics"""
        return {
            'statistics': self.stats.copy(),
            'recent_errors': len([e for e in self.error_history 
                                if (datetime.now() - e.timestamp).total_seconds() < 3600]),
            'total_errors_logged': len(self.error_history),
            'recovery_strategies': list(self.recovery_strategies.keys()),
            'failure_thresholds': self.failure_thresholds
        }
    
    def cleanup_old_errors(self, max_age_hours: int = 24):
        """Clean up old error records"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        self.error_history = [e for e in self.error_history if e.timestamp > cutoff_time]
        logger.debug(f"Cleaned up old error records, {len(self.error_history)} remaining")
