#!/usr/bin/env python3
"""
Balkland.com PROXY TEST & VERIFICATION
Tests your proxy: **************:57083:proxidize-OlDQTRHh1:SjYtiWBd
Verifies real Google traffic generation and proves authenticity
"""

import asyncio
import aiohttp
import requests
import time
from datetime import datetime

class ProxyTestVerification:
    """Test and verify your proxy for real traffic generation"""
    
    def __init__(self):
        print("🔍 BALKLAND PROXY TEST & VERIFICATION")
        print("=" * 60)
        
        # Your proxy
        self.proxy = {
            'host': '**************',
            'port': '57083',
            'username': 'proxidize-OlDQTRHh1',
            'password': 'SjYtiWBd'
        }
        
        self.proxy_url = f"http://{self.proxy['username']}:{self.proxy['password']}@{self.proxy['host']}:{self.proxy['port']}"
        
        print(f"🔗 Testing Proxy: {self.proxy['host']}:{self.proxy['port']}")
        print("=" * 60)
    
    async def test_proxy_basic(self):
        """Test basic proxy connectivity"""
        print("🔧 Testing basic proxy connectivity...")
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=15)) as session:
                async with session.get('https://httpbin.org/ip', proxy=self.proxy_url) as response:
                    if response.status == 200:
                        data = await response.json()
                        real_ip = data.get('origin', 'Unknown')
                        print(f"✅ PROXY WORKING: Real IP = {real_ip}")
                        return True, real_ip
                    else:
                        print(f"❌ PROXY FAILED: HTTP {response.status}")
                        return False, None
        except Exception as e:
            print(f"❌ PROXY ERROR: {e}")
            return False, None
    
    async def test_proxy_location(self):
        """Test proxy location (US verification)"""
        print("🌍 Testing proxy location...")
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=15)) as session:
                async with session.get('https://ipapi.co/json/', proxy=self.proxy_url) as response:
                    if response.status == 200:
                        data = await response.json()
                        country = data.get('country_name', 'Unknown')
                        region = data.get('region', 'Unknown')
                        city = data.get('city', 'Unknown')
                        
                        print(f"✅ LOCATION: {city}, {region}, {country}")
                        
                        if 'United States' in country or data.get('country_code') == 'US':
                            print("✅ US LOCATION CONFIRMED")
                            return True, f"{city}, {region}"
                        else:
                            print(f"⚠️ NON-US LOCATION: {country}")
                            return False, f"{city}, {region}"
                    else:
                        print(f"❌ LOCATION CHECK FAILED: HTTP {response.status}")
                        return False, None
        except Exception as e:
            print(f"❌ LOCATION ERROR: {e}")
            return False, None
    
    async def test_google_access(self):
        """Test Google access through proxy"""
        print("🔍 Testing Google access...")
        
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Connection': 'keep-alive',
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=20), headers=headers) as session:
                # Test simple Google search
                search_url = "https://www.google.com/search?q=test+search&num=10"
                
                async with session.get(search_url, proxy=self.proxy_url) as response:
                    print(f"📊 Google Response: HTTP {response.status}")
                    
                    if response.status == 200:
                        content = await response.text()
                        content_size = len(content)
                        
                        print(f"📄 Response Size: {content_size} bytes")
                        
                        # Check for Google indicators
                        google_indicators = [
                            'google.com',
                            'Search',
                            'results',
                            'class="g"',
                            'data-ved='
                        ]
                        
                        found_indicators = []
                        for indicator in google_indicators:
                            if indicator.lower() in content.lower():
                                found_indicators.append(indicator)
                        
                        print(f"🔍 Google Indicators Found: {len(found_indicators)}/5")
                        for indicator in found_indicators:
                            print(f"   ✅ {indicator}")
                        
                        if len(found_indicators) >= 3 and content_size > 5000:
                            print("✅ REAL GOOGLE ACCESS CONFIRMED")
                            return True, content_size
                        else:
                            print("⚠️ LIMITED GOOGLE ACCESS (might be blocked/filtered)")
                            return False, content_size
                    
                    elif response.status == 429:
                        print("⚠️ RATE LIMITED: Google is rate limiting requests")
                        return False, 0
                    elif response.status == 403:
                        print("⚠️ BLOCKED: Google is blocking the proxy")
                        return False, 0
                    else:
                        print(f"❌ GOOGLE ACCESS FAILED: HTTP {response.status}")
                        return False, 0
                        
        except Exception as e:
            print(f"❌ GOOGLE TEST ERROR: {e}")
            return False, 0
    
    async def test_balkland_search(self):
        """Test specific Balkland search"""
        print("🎯 Testing Balkland search...")
        
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Connection': 'keep-alive',
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=25), headers=headers) as session:
                # Test Balkland search
                search_url = "https://www.google.com/search?q=Balkland+balkan+tour&num=20"
                
                print(f"🔍 Searching: Balkland balkan tour")
                
                async with session.get(search_url, proxy=self.proxy_url) as response:
                    print(f"📊 Balkland Search Response: HTTP {response.status}")
                    
                    if response.status == 200:
                        content = await response.text()
                        content_size = len(content)
                        
                        print(f"📄 Response Size: {content_size} bytes")
                        
                        # Check for Balkland in results
                        balkland_found = 'balkland' in content.lower()
                        print(f"🎯 Balkland Found in Results: {balkland_found}")
                        
                        # Check for real Google structure
                        google_structure = [
                            'search?q=',
                            'class="g"',
                            'data-ved=',
                            'About ',
                            'results'
                        ]
                        
                        structure_score = sum(1 for item in google_structure if item.lower() in content.lower())
                        print(f"🏗️ Google Structure Score: {structure_score}/5")
                        
                        if structure_score >= 3 and content_size > 8000:
                            print("✅ REAL BALKLAND SEARCH CONFIRMED")
                            
                            # Simulate real user behavior
                            await asyncio.sleep(3)
                            print("⏱️ Simulated 3-second page view")
                            
                            return True, balkland_found, content_size
                        else:
                            print("⚠️ SEARCH RESPONSE QUESTIONABLE")
                            return False, balkland_found, content_size
                    else:
                        print(f"❌ BALKLAND SEARCH FAILED: HTTP {response.status}")
                        return False, False, 0
                        
        except Exception as e:
            print(f"❌ BALKLAND SEARCH ERROR: {e}")
            return False, False, 0
    
    async def run_comprehensive_test(self):
        """Run comprehensive proxy and traffic test"""
        print("🚀 STARTING COMPREHENSIVE PROXY TEST...")
        print("=" * 60)
        
        start_time = time.time()
        
        # Test 1: Basic connectivity
        basic_ok, real_ip = await self.test_proxy_basic()
        if not basic_ok:
            print("❌ BASIC TEST FAILED - Cannot proceed")
            return
        
        print()
        
        # Test 2: Location verification
        location_ok, location = await self.test_proxy_location()
        print()
        
        # Test 3: Google access
        google_ok, response_size = await self.test_google_access()
        print()
        
        # Test 4: Balkland search
        balkland_ok, balkland_found, balkland_size = await self.test_balkland_search()
        print()
        
        # Summary
        duration = time.time() - start_time
        
        print("📊 COMPREHENSIVE TEST RESULTS:")
        print("=" * 60)
        print(f"⏱️ Test Duration: {duration:.1f} seconds")
        print(f"🔗 Proxy IP: {real_ip}")
        print(f"🌍 Location: {location} ({'US ✅' if location_ok else 'Non-US ⚠️'})")
        print(f"🔍 Google Access: {'Working ✅' if google_ok else 'Limited ⚠️'}")
        print(f"🎯 Balkland Search: {'Working ✅' if balkland_ok else 'Limited ⚠️'}")
        print(f"📄 Response Sizes: Google={response_size}, Balkland={balkland_size}")
        print(f"🎯 Balkland Found: {'Yes ✅' if balkland_found else 'No ❌'}")
        
        # Overall assessment
        total_score = sum([basic_ok, location_ok, google_ok, balkland_ok])
        
        print(f"\n🏆 OVERALL SCORE: {total_score}/4")
        
        if total_score >= 3:
            print("✅ PROXY EXCELLENT: Ready for real traffic generation")
            print("🚀 RECOMMENDATION: Proceed with traffic campaign")
        elif total_score >= 2:
            print("⚠️ PROXY GOOD: Some limitations but usable")
            print("🔄 RECOMMENDATION: Proceed with caution")
        else:
            print("❌ PROXY ISSUES: Significant problems detected")
            print("🛠️ RECOMMENDATION: Check proxy configuration")
        
        print("=" * 60)

async def main():
    """Main test function"""
    tester = ProxyTestVerification()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
