"""
High-Volume Traffic Scheduler

This module creates realistic session distribution across 24 hours with natural
traffic patterns for 30-40k daily impressions and 50-60 clicks.
"""

import asyncio
import random
from datetime import datetime, timedelta, time
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, field
import math
from loguru import logger

@dataclass
class HourlyPlan:
    """Hourly traffic distribution plan"""
    hour: int
    target_sessions: int
    impression_sessions: int
    click_sessions: int
    batches: List[List[Dict[str, Any]]] = field(default_factory=list)

@dataclass
class HighVolumePlan:
    """High-volume daily traffic plan"""
    date: datetime
    total_impressions: int
    total_clicks: int
    hourly_plans: Dict[int, HourlyPlan] = field(default_factory=dict)
    execution_status: str = "planned"  # planned, running, completed, failed

class HighVolumeScheduler:
    """High-volume traffic scheduler with realistic distribution"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize high-volume scheduler"""
        self.config = config
        self.traffic_config = config['traffic']
        self.scheduling_config = config['scheduling']
        
        # High-volume targets
        self.daily_impressions = self.traffic_config['daily_impressions']
        self.daily_clicks = self.traffic_config['daily_clicks']
        
        # Hourly distribution from config
        self.hourly_distribution = self.scheduling_config['hourly_distribution']
        
        # Session timing
        self.session_timing = self.scheduling_config['session_timing']
        
        # Current execution state
        self.current_plan: Optional[HighVolumePlan] = None
        self.is_running = False
        self.execution_stats = {
            'sessions_completed': 0,
            'sessions_failed': 0,
            'impressions_generated': 0,
            'clicks_generated': 0,
            'start_time': None,
            'current_hour': None
        }
        
        logger.info(f"High-volume scheduler initialized - Target: {self.daily_impressions} impressions, {self.daily_clicks} clicks")
    
    def generate_high_volume_plan(self, target_date: datetime = None) -> HighVolumePlan:
        """Generate high-volume traffic distribution plan"""
        try:
            if target_date is None:
                target_date = datetime.now().date()
            
            logger.info(f"Generating high-volume plan for {target_date}")
            
            # Create plan
            plan = HighVolumePlan(
                date=target_date,
                total_impressions=self.daily_impressions,
                total_clicks=self.daily_clicks
            )
            
            # Distribute sessions across hours
            self._distribute_hourly_sessions(plan)
            
            # Create detailed hourly plans
            self._create_hourly_plans(plan)
            
            # Organize into execution batches
            self._organize_execution_batches(plan)
            
            self.current_plan = plan
            
            logger.info(f"High-volume plan generated: {plan.total_impressions + plan.total_clicks} total sessions across 24 hours")
            return plan
            
        except Exception as e:
            logger.error(f"Error generating high-volume plan: {e}")
            raise
    
    def _distribute_hourly_sessions(self, plan: HighVolumePlan):
        """Distribute sessions across 24 hours based on realistic patterns"""
        try:
            total_sessions = plan.total_impressions + plan.total_clicks
            
            # Calculate sessions per hour based on distribution
            for hour_str, percentage in self.hourly_distribution.items():
                hour = int(hour_str)
                
                # Calculate total sessions for this hour
                hour_sessions = int(total_sessions * percentage)
                
                # Determine impression vs click ratio for this hour
                # Clicks are more likely during business hours
                if 9 <= hour <= 17:  # Business hours
                    click_ratio = self.traffic_config['target_ctr'] * 1.2  # 20% higher during business
                elif 18 <= hour <= 22:  # Evening
                    click_ratio = self.traffic_config['target_ctr'] * 1.0  # Normal ratio
                else:  # Night/early morning
                    click_ratio = self.traffic_config['target_ctr'] * 0.8  # 20% lower at night
                
                # Calculate clicks and impressions for this hour
                hour_clicks = min(int(hour_sessions * click_ratio), 
                                plan.total_clicks - sum(hp.click_sessions for hp in plan.hourly_plans.values()))
                hour_impressions = hour_sessions - hour_clicks
                
                # Ensure we don't exceed daily limits
                remaining_clicks = plan.total_clicks - sum(hp.click_sessions for hp in plan.hourly_plans.values())
                remaining_impressions = plan.total_impressions - sum(hp.impression_sessions for hp in plan.hourly_plans.values())
                
                hour_clicks = min(hour_clicks, remaining_clicks)
                hour_impressions = min(hour_impressions, remaining_impressions)
                
                # Create hourly plan
                plan.hourly_plans[hour] = HourlyPlan(
                    hour=hour,
                    target_sessions=hour_clicks + hour_impressions,
                    impression_sessions=hour_impressions,
                    click_sessions=hour_clicks
                )
            
            # Distribute any remaining sessions
            self._distribute_remaining_sessions(plan)
            
        except Exception as e:
            logger.error(f"Error distributing hourly sessions: {e}")
            raise
    
    def _distribute_remaining_sessions(self, plan: HighVolumePlan):
        """Distribute any remaining sessions to peak hours"""
        try:
            # Calculate remaining sessions
            total_planned_impressions = sum(hp.impression_sessions for hp in plan.hourly_plans.values())
            total_planned_clicks = sum(hp.click_sessions for hp in plan.hourly_plans.values())
            
            remaining_impressions = plan.total_impressions - total_planned_impressions
            remaining_clicks = plan.total_clicks - total_planned_clicks
            
            # Distribute remaining to peak hours (9-17)
            peak_hours = [h for h in range(9, 18) if h in plan.hourly_plans]
            
            if remaining_impressions > 0 and peak_hours:
                impressions_per_hour = remaining_impressions // len(peak_hours)
                extra_impressions = remaining_impressions % len(peak_hours)
                
                for i, hour in enumerate(peak_hours):
                    additional = impressions_per_hour + (1 if i < extra_impressions else 0)
                    plan.hourly_plans[hour].impression_sessions += additional
                    plan.hourly_plans[hour].target_sessions += additional
            
            if remaining_clicks > 0 and peak_hours:
                clicks_per_hour = remaining_clicks // len(peak_hours)
                extra_clicks = remaining_clicks % len(peak_hours)
                
                for i, hour in enumerate(peak_hours):
                    additional = clicks_per_hour + (1 if i < extra_clicks else 0)
                    plan.hourly_plans[hour].click_sessions += additional
                    plan.hourly_plans[hour].target_sessions += additional
                    
        except Exception as e:
            logger.debug(f"Error distributing remaining sessions: {e}")
    
    def _create_hourly_plans(self, plan: HighVolumePlan):
        """Create detailed hourly execution plans"""
        try:
            from brand_keyword_engine import BrandKeywordEngine
            
            # Initialize keyword engine
            keyword_engine = BrandKeywordEngine(self.config)
            
            for hour, hourly_plan in plan.hourly_plans.items():
                if hourly_plan.target_sessions == 0:
                    continue
                
                # Generate session configurations
                session_configs = []
                
                # Generate impression sessions
                for _ in range(hourly_plan.impression_sessions):
                    query = keyword_engine.generate_search_query("impression")
                    session_config = self._create_session_config(query, "impression", hour)
                    session_configs.append(session_config)
                
                # Generate click sessions
                for _ in range(hourly_plan.click_sessions):
                    query = keyword_engine.generate_search_query("click")
                    session_config = self._create_session_config(query, "click", hour)
                    session_configs.append(session_config)
                
                # Shuffle to randomize order
                random.shuffle(session_configs)
                
                # Store in hourly plan
                hourly_plan.session_configs = session_configs
                
        except Exception as e:
            logger.error(f"Error creating hourly plans: {e}")
            raise
    
    def _create_session_config(self, query: Any, session_type: str, hour: int) -> Dict[str, Any]:
        """Create session configuration"""
        try:
            # Select region based on distribution
            region_dist = self.config['regions']
            all_regions = []
            all_weights = []
            
            for region_type in ['primary', 'secondary']:
                for region, weight in region_dist[region_type].items():
                    all_regions.append(region)
                    all_weights.append(weight)
            
            selected_region = random.choices(all_regions, weights=all_weights)[0]
            
            # Select device type
            device_dist = self.config['devices']
            device_choices = list(device_dist.keys())
            device_weights = list(device_dist.values())
            selected_device = random.choices(device_choices, weights=device_weights)[0]
            
            # Create session config
            return {
                'session_type': session_type,
                'keyword': query.query,
                'query_metadata': {
                    'keyword_type': query.keyword_type,
                    'intent': query.intent,
                    'expected_ctr': query.expected_ctr
                },
                'target_url': self.config['target']['url'],
                'region': selected_region,
                'device_type': selected_device,
                'proxy_type': 'mobile' if selected_device == 'mobile' else 'datacenter',
                'scheduled_hour': hour,
                'scheduled_time': self._generate_random_time_in_hour(hour)
            }
            
        except Exception as e:
            logger.debug(f"Error creating session config: {e}")
            return {
                'session_type': session_type,
                'keyword': getattr(query, 'query', 'default keyword'),
                'target_url': self.config['target']['url'],
                'region': 'US',
                'device_type': 'desktop',
                'scheduled_hour': hour
            }
    
    def _generate_random_time_in_hour(self, hour: int) -> datetime:
        """Generate random time within specified hour"""
        try:
            today = datetime.now().date()
            minute = random.randint(0, 59)
            second = random.randint(0, 59)
            return datetime.combine(today, time(hour, minute, second))
        except:
            return datetime.now()
    
    def _organize_execution_batches(self, plan: HighVolumePlan):
        """Organize sessions into execution batches"""
        try:
            batch_config = self.session_timing
            
            for hour, hourly_plan in plan.hourly_plans.items():
                if not hasattr(hourly_plan, 'session_configs'):
                    continue
                
                sessions = hourly_plan.session_configs
                if not sessions:
                    continue
                
                # Calculate batch parameters
                batches_per_hour = random.randint(*batch_config['batches_per_hour'])
                sessions_per_batch = len(sessions) // batches_per_hour
                
                if sessions_per_batch == 0:
                    # If too few sessions, create one batch
                    hourly_plan.batches = [sessions]
                    continue
                
                # Create batches
                batches = []
                remaining_sessions = sessions.copy()
                
                for i in range(batches_per_hour):
                    if i == batches_per_hour - 1:
                        # Last batch gets all remaining sessions
                        batch = remaining_sessions
                    else:
                        # Apply variance to batch size
                        variance = random.uniform(0.8, 1.2)
                        batch_size = max(1, int(sessions_per_batch * variance))
                        batch_size = min(batch_size, len(remaining_sessions))
                        batch = remaining_sessions[:batch_size]
                        remaining_sessions = remaining_sessions[batch_size:]
                    
                    if batch:
                        batches.append(batch)
                
                hourly_plan.batches = batches
                
        except Exception as e:
            logger.error(f"Error organizing execution batches: {e}")
    
    async def execute_high_volume_plan(self, plan: HighVolumePlan = None):
        """Execute high-volume traffic plan"""
        try:
            if plan is None:
                plan = self.current_plan
            
            if plan is None:
                raise ValueError("No plan available for execution")
            
            self.is_running = True
            plan.execution_status = "running"
            self.execution_stats['start_time'] = datetime.now()
            
            logger.info(f"Starting high-volume plan execution for {plan.date}")
            
            # Execute each hour
            for hour in range(24):
                if not self.is_running:
                    break
                
                if hour not in plan.hourly_plans:
                    continue
                
                hourly_plan = plan.hourly_plans[hour]
                if hourly_plan.target_sessions == 0:
                    continue
                
                # Wait until the correct hour (if running in real-time)
                await self._wait_for_hour(hour)
                
                # Execute hourly plan
                await self._execute_hourly_plan(hourly_plan)
            
            plan.execution_status = "completed"
            logger.info(f"High-volume plan execution completed")
            
        except Exception as e:
            logger.error(f"Error executing high-volume plan: {e}")
            if plan:
                plan.execution_status = "failed"
            raise
        finally:
            self.is_running = False
    
    async def _wait_for_hour(self, target_hour: int):
        """Wait until target hour (for real-time execution)"""
        try:
            current_time = datetime.now()
            current_hour = current_time.hour
            
            if current_hour == target_hour:
                return  # Already at target hour
            
            # Calculate time to wait
            if target_hour > current_hour:
                # Same day
                target_time = current_time.replace(hour=target_hour, minute=0, second=0, microsecond=0)
            else:
                # Next day
                tomorrow = current_time + timedelta(days=1)
                target_time = tomorrow.replace(hour=target_hour, minute=0, second=0, microsecond=0)
            
            wait_seconds = (target_time - current_time).total_seconds()
            
            if wait_seconds > 0:
                logger.info(f"Waiting {wait_seconds/60:.1f} minutes until hour {target_hour}")
                await asyncio.sleep(wait_seconds)
                
        except Exception as e:
            logger.debug(f"Error waiting for hour: {e}")
    
    async def _execute_hourly_plan(self, hourly_plan: HourlyPlan):
        """Execute hourly traffic plan"""
        try:
            self.execution_stats['current_hour'] = hourly_plan.hour
            logger.info(f"Executing hour {hourly_plan.hour}: {hourly_plan.target_sessions} sessions")
            
            # Import impression/click manager
            from impression_click_manager import ImpressionClickManager
            
            # Initialize manager
            ic_manager = ImpressionClickManager(self.config)
            
            # Execute all batches for this hour
            for i, batch in enumerate(hourly_plan.batches):
                if not self.is_running:
                    break
                
                logger.debug(f"Executing batch {i+1}/{len(hourly_plan.batches)} with {len(batch)} sessions")
                
                # Execute batch
                await self._execute_batch(batch, ic_manager)
                
                # Delay between batches
                if i < len(hourly_plan.batches) - 1:
                    delay = random.uniform(
                        self.session_timing['min_delay_between_sessions'],
                        self.session_timing['max_delay_between_sessions']
                    )
                    await asyncio.sleep(delay)
            
            logger.info(f"Hour {hourly_plan.hour} completed")
            
        except Exception as e:
            logger.error(f"Error executing hourly plan: {e}")
    
    async def _execute_batch(self, batch: List[Dict[str, Any]], ic_manager):
        """Execute batch of sessions"""
        try:
            # Create semaphore for concurrency control
            max_concurrent = min(50, len(batch))  # Limit concurrent sessions
            semaphore = asyncio.Semaphore(max_concurrent)
            
            # Create tasks for all sessions in batch
            tasks = []
            for session_config in batch:
                task = asyncio.create_task(
                    self._execute_session_with_semaphore(session_config, ic_manager, semaphore)
                )
                tasks.append(task)
            
            # Execute all sessions
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for session_config, result in zip(batch, results):
                if isinstance(result, Exception):
                    logger.error(f"Session failed: {result}")
                    self.execution_stats['sessions_failed'] += 1
                else:
                    self.execution_stats['sessions_completed'] += 1
                    if session_config['session_type'] == 'impression':
                        self.execution_stats['impressions_generated'] += 1
                    else:
                        self.execution_stats['clicks_generated'] += 1
            
        except Exception as e:
            logger.error(f"Error executing batch: {e}")
    
    async def _execute_session_with_semaphore(self, session_config: Dict[str, Any], 
                                            ic_manager, semaphore: asyncio.Semaphore):
        """Execute session with concurrency control"""
        async with semaphore:
            try:
                if session_config['session_type'] == 'impression':
                    return await ic_manager.execute_impression_session(session_config)
                else:
                    return await ic_manager.execute_click_session(session_config)
            except Exception as e:
                logger.debug(f"Session execution error: {e}")
                raise
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get execution statistics"""
        return {
            'execution_stats': self.execution_stats.copy(),
            'plan_status': self.current_plan.execution_status if self.current_plan else "no_plan",
            'is_running': self.is_running,
            'daily_targets': {
                'target_impressions': self.daily_impressions,
                'target_clicks': self.daily_clicks,
                'progress_impressions': self.execution_stats['impressions_generated'] / self.daily_impressions,
                'progress_clicks': self.execution_stats['clicks_generated'] / self.daily_clicks
            },
            'current_hour': self.execution_stats.get('current_hour'),
            'success_rate': (
                self.execution_stats['sessions_completed'] / 
                max(1, self.execution_stats['sessions_completed'] + self.execution_stats['sessions_failed'])
            )
        }
    
    def stop_execution(self):
        """Stop execution"""
        self.is_running = False
        if self.current_plan:
            self.current_plan.execution_status = "stopped"
        logger.info("High-volume execution stopped")
    
    def reset_execution_stats(self):
        """Reset execution statistics"""
        self.execution_stats = {
            'sessions_completed': 0,
            'sessions_failed': 0,
            'impressions_generated': 0,
            'clicks_generated': 0,
            'start_time': None,
            'current_hour': None
        }
        logger.info("Execution statistics reset")
