"""
Advanced Organic Traffic Generation System - Configuration Manager

This module handles all configuration loading, validation, and management
for the traffic generation system.
"""

import os
import yaml
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from pathlib import Path
from dotenv import load_dotenv
from loguru import logger

# Load environment variables
load_dotenv()

@dataclass
class TargetConfig:
    """Target website configuration"""
    url: str
    domain: str
    
    def __post_init__(self):
        if not self.url.startswith(('http://', 'https://')):
            raise ValueError("Target URL must include protocol (http:// or https://)")

@dataclass
class KeywordConfig:
    """Keywords configuration with priority levels"""
    primary: List[str] = field(default_factory=list)
    secondary: List[str] = field(default_factory=list)
    longtail: List[str] = field(default_factory=list)
    
    @property
    def all_keywords(self) -> List[str]:
        """Get all keywords combined"""
        return self.primary + self.secondary + self.longtail
    
    def get_keywords_by_priority(self, priority: str) -> List[str]:
        """Get keywords by priority level"""
        return getattr(self, priority, [])

@dataclass
class TrafficConfig:
    """Traffic volume and distribution configuration"""
    daily_volume: int
    clicks_per_keyword: Dict[str, int]
    distribution: Dict[str, float]
    
    def __post_init__(self):
        # Validate distribution percentages sum to 1.0
        total = sum(self.distribution.values())
        if abs(total - 1.0) > 0.01:
            raise ValueError(f"Distribution percentages must sum to 1.0, got {total}")

@dataclass
class ProxyConfig:
    """Proxy configuration"""
    provider: str
    api_key: str
    rotation_interval: int
    max_failures: int
    types: List[str]
    
    def __post_init__(self):
        if not self.api_key:
            raise ValueError("Proxy API key is required")

@dataclass
class BehaviorConfig:
    """Human behavior simulation configuration"""
    search_delays: Dict[str, List[float]]
    scrolling: Dict[str, List[float]]
    reading: Dict[str, int]
    mouse: Dict[str, Any]

@dataclass
class SchedulingConfig:
    """Traffic scheduling configuration"""
    operating_hours: Dict[str, int]
    sessions_per_day: List[int]
    batch_size_variance: float
    rate_limiting: Dict[str, Any]

@dataclass
class AnalyticsConfig:
    """Analytics and logging configuration"""
    log_level: str
    log_file: str
    metrics: List[str]
    reports: Dict[str, Any]

@dataclass
class ErrorHandlingConfig:
    """Error handling and recovery configuration"""
    max_retries: int
    retry_delay: List[int]
    thresholds: Dict[str, float]
    recovery: Dict[str, Any]

@dataclass
class ComplianceConfig:
    """Compliance and safety configuration"""
    respect_robots_txt: bool
    user_agent_rotation: bool
    request_rate_limit: int
    safety: Dict[str, Any]

class ConfigManager:
    """Main configuration manager class"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize configuration manager"""
        self.config_path = Path(config_path)
        self._config_data = None
        self._load_config()
        self._validate_config()
    
    def _load_config(self):
        """Load configuration from YAML file"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config_data = yaml.safe_load(f)
            logger.info(f"Configuration loaded from {self.config_path}")
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {self.config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"Error parsing YAML configuration: {e}")
            raise
    
    def _validate_config(self):
        """Validate configuration data"""
        required_sections = [
            'target', 'keywords', 'traffic', 'regions', 'proxy',
            'fingerprinting', 'behavior', 'scheduling', 'analytics',
            'error_handling', 'compliance'
        ]
        
        for section in required_sections:
            if section not in self._config_data:
                raise ValueError(f"Missing required configuration section: {section}")
        
        logger.info("Configuration validation passed")
    
    def _substitute_env_vars(self, value: Any) -> Any:
        """Substitute environment variables in configuration values"""
        if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
            env_var = value[2:-1]
            env_value = os.getenv(env_var)
            if env_value is None:
                raise ValueError(f"Environment variable {env_var} not found")
            return env_value
        elif isinstance(value, dict):
            return {k: self._substitute_env_vars(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [self._substitute_env_vars(item) for item in value]
        return value
    
    @property
    def target(self) -> TargetConfig:
        """Get target configuration"""
        target_data = self._config_data['target']
        return TargetConfig(
            url=target_data['url'],
            domain=target_data['domain']
        )
    
    @property
    def keywords(self) -> KeywordConfig:
        """Get keywords configuration"""
        keywords_data = self._config_data['keywords']
        return KeywordConfig(
            primary=keywords_data.get('primary', []),
            secondary=keywords_data.get('secondary', []),
            longtail=keywords_data.get('longtail', [])
        )
    
    @property
    def traffic(self) -> TrafficConfig:
        """Get traffic configuration"""
        traffic_data = self._config_data['traffic']
        return TrafficConfig(
            daily_volume=traffic_data['daily_volume'],
            clicks_per_keyword=traffic_data['clicks_per_keyword'],
            distribution=traffic_data['distribution']
        )
    
    @property
    def regions(self) -> Dict[str, List[str]]:
        """Get geographic regions configuration"""
        return self._config_data['regions']
    
    @property
    def proxy(self) -> ProxyConfig:
        """Get proxy configuration"""
        proxy_data = self._config_data['proxy']
        api_key = self._substitute_env_vars(proxy_data['api_key'])
        
        return ProxyConfig(
            provider=proxy_data['provider'],
            api_key=api_key,
            rotation_interval=proxy_data['rotation_interval'],
            max_failures=proxy_data['max_failures'],
            types=proxy_data['types']
        )
    
    @property
    def fingerprinting(self) -> Dict[str, Any]:
        """Get fingerprinting configuration"""
        return self._config_data['fingerprinting']
    
    @property
    def behavior(self) -> BehaviorConfig:
        """Get behavior simulation configuration"""
        behavior_data = self._config_data['behavior']
        return BehaviorConfig(
            search_delays=behavior_data['search_delays'],
            scrolling=behavior_data['scrolling'],
            reading=behavior_data['reading'],
            mouse=behavior_data['mouse']
        )
    
    @property
    def scheduling(self) -> SchedulingConfig:
        """Get scheduling configuration"""
        scheduling_data = self._config_data['scheduling']
        return SchedulingConfig(
            operating_hours=scheduling_data['operating_hours'],
            sessions_per_day=scheduling_data['sessions_per_day'],
            batch_size_variance=scheduling_data['batch_size_variance'],
            rate_limiting=scheduling_data['rate_limiting']
        )
    
    @property
    def analytics(self) -> AnalyticsConfig:
        """Get analytics configuration"""
        analytics_data = self._config_data['analytics']
        return AnalyticsConfig(
            log_level=analytics_data['log_level'],
            log_file=analytics_data['log_file'],
            metrics=analytics_data['metrics'],
            reports=analytics_data['reports']
        )
    
    @property
    def error_handling(self) -> ErrorHandlingConfig:
        """Get error handling configuration"""
        error_data = self._config_data['error_handling']
        return ErrorHandlingConfig(
            max_retries=error_data['max_retries'],
            retry_delay=error_data['retry_delay'],
            thresholds=error_data['thresholds'],
            recovery=error_data['recovery']
        )
    
    @property
    def compliance(self) -> ComplianceConfig:
        """Get compliance configuration"""
        compliance_data = self._config_data['compliance']
        return ComplianceConfig(
            respect_robots_txt=compliance_data['respect_robots_txt'],
            user_agent_rotation=compliance_data['user_agent_rotation'],
            request_rate_limit=compliance_data['request_rate_limit'],
            safety=compliance_data['safety']
        )
    
    def get_environment_config(self) -> Dict[str, Any]:
        """Get environment-specific configuration"""
        return {
            'environment': os.getenv('ENVIRONMENT', 'production'),
            'debug': os.getenv('DEBUG', 'false').lower() == 'true',
            'log_level': os.getenv('LOG_LEVEL', 'INFO'),
            'max_concurrent_sessions': int(os.getenv('MAX_CONCURRENT_SESSIONS', '10')),
            'max_requests_per_minute': int(os.getenv('MAX_REQUESTS_PER_MINUTE', '120')),
            'browser_data_dir': os.getenv('BROWSER_DATA_DIR', './browser_profiles'),
            'report_export_path': os.getenv('REPORT_EXPORT_PATH', './reports')
        }
    
    def export_config(self, output_path: str = "config_export.json"):
        """Export current configuration to JSON file"""
        config_dict = {
            'target': self.target.__dict__,
            'keywords': self.keywords.__dict__,
            'traffic': self.traffic.__dict__,
            'regions': self.regions,
            'proxy': {k: v for k, v in self.proxy.__dict__.items() if k != 'api_key'},
            'fingerprinting': self.fingerprinting,
            'behavior': self.behavior.__dict__,
            'scheduling': self.scheduling.__dict__,
            'analytics': self.analytics.__dict__,
            'error_handling': self.error_handling.__dict__,
            'compliance': self.compliance.__dict__,
            'environment': self.get_environment_config()
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, default=str)
        
        logger.info(f"Configuration exported to {output_path}")
    
    def validate_target_url(self) -> bool:
        """Validate that target URL is accessible"""
        import requests
        try:
            response = requests.head(self.target.url, timeout=10)
            return response.status_code < 400
        except requests.RequestException:
            return False

# Global configuration instance
config = ConfigManager()
