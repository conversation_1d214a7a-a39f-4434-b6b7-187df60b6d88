
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 10 },
    { duration: '5m', target: 20 },
    { duration: '2m', target: 0 },
  ],
};

const keywords = [
  'Balkland balkan tour',
  'Balkland balkan tour packages',
  'Balkland balkan tours',
  'book Balkland tour'
];

export default function() {
  const keyword = keywords[Math.floor(Math.random() * keywords.length)];
  
  const params = {
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    },
  };
  
  const response = http.get(`https://www.google.com/search?q=${encodeURIComponent(keyword)}&num=20`, params);
  
  check(response, {
    'status is 200': (r) => r.status === 200,
    'contains Balkland': (r) => r.body.toLowerCase().includes('balkland'),
  });
  
  sleep(Math.random() * 5 + 3);
}
