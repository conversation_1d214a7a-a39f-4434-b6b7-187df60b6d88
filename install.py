#!/usr/bin/env python3
"""
High-Volume Organic Traffic Generation System - Installation Script

This script automates the complete installation and setup process for the
high-volume traffic generation system.
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path
import yaml
import json

class HighVolumeInstaller:
    """Automated installer for high-volume traffic system"""
    
    def __init__(self):
        """Initialize installer"""
        self.system = platform.system().lower()
        self.python_version = sys.version_info
        self.project_root = Path.cwd()
        
        print("🚀 High-Volume Organic Traffic Generation System Installer")
        print("=" * 60)
        print(f"System: {platform.system()} {platform.release()}")
        print(f"Python: {sys.version}")
        print(f"Project Root: {self.project_root}")
        print()
    
    def run_installation(self):
        """Run complete installation process"""
        try:
            print("Starting installation process...")
            
            # Step 1: Check system requirements
            self.check_system_requirements()
            
            # Step 2: Install Python dependencies
            self.install_python_dependencies()
            
            # Step 3: Install Playwright browsers
            self.install_playwright_browsers()
            
            # Step 4: Create configuration files
            self.create_configuration_files()
            
            # Step 5: Setup environment variables
            self.setup_environment_variables()
            
            # Step 6: Create directories
            self.create_directories()
            
            # Step 7: Validate installation
            self.validate_installation()
            
            # Step 8: Show next steps
            self.show_next_steps()
            
            print("\n✅ Installation completed successfully!")
            
        except Exception as e:
            print(f"\n❌ Installation failed: {e}")
            sys.exit(1)
    
    def check_system_requirements(self):
        """Check system requirements"""
        print("📋 Checking system requirements...")
        
        # Check Python version
        if self.python_version < (3, 8):
            raise Exception("Python 3.8+ is required")
        print(f"  ✅ Python {self.python_version.major}.{self.python_version.minor}")
        
        # Check available memory
        try:
            import psutil
            memory_gb = psutil.virtual_memory().total / (1024**3)
            if memory_gb < 4:
                print(f"  ⚠️  Warning: Only {memory_gb:.1f}GB RAM available (4GB+ recommended)")
            else:
                print(f"  ✅ Memory: {memory_gb:.1f}GB")
        except ImportError:
            print("  ⚠️  Cannot check memory (psutil not installed)")
        
        # Check disk space
        disk_space = shutil.disk_usage(self.project_root).free / (1024**3)
        if disk_space < 10:
            print(f"  ⚠️  Warning: Only {disk_space:.1f}GB disk space available (10GB+ recommended)")
        else:
            print(f"  ✅ Disk Space: {disk_space:.1f}GB available")
        
        print("  ✅ System requirements check completed")
    
    def install_python_dependencies(self):
        """Install Python dependencies"""
        print("\n📦 Installing Python dependencies...")
        
        try:
            # Upgrade pip first
            subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            print("  ✅ pip upgraded")
            
            # Install requirements
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                         check=True, capture_output=True)
            print("  ✅ Python dependencies installed")
            
        except subprocess.CalledProcessError as e:
            print(f"  ❌ Failed to install dependencies: {e}")
            print("  💡 Try running: pip install -r requirements.txt")
            raise
    
    def install_playwright_browsers(self):
        """Install Playwright browsers"""
        print("\n🌐 Installing Playwright browsers...")
        
        try:
            # Install Playwright browsers
            subprocess.run([sys.executable, "-m", "playwright", "install", "chromium"], 
                         check=True, capture_output=True)
            print("  ✅ Chromium browser installed")
            
            # Install system dependencies
            if self.system == "linux":
                subprocess.run([sys.executable, "-m", "playwright", "install-deps"], 
                             check=True, capture_output=True)
                print("  ✅ System dependencies installed")
            
        except subprocess.CalledProcessError as e:
            print(f"  ❌ Failed to install browsers: {e}")
            print("  💡 Try running: playwright install chromium")
            raise
    
    def create_configuration_files(self):
        """Create configuration files"""
        print("\n⚙️  Creating configuration files...")
        
        # Create .env file if it doesn't exist
        env_file = self.project_root / ".env"
        if not env_file.exists():
            env_content = """# High-Volume Traffic Generation Environment Variables

# Proxy Configuration (REQUIRED)
PROXY_API_KEY=your_proxy_api_key_here
BACKUP_PROXY_API_KEY=your_backup_proxy_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///traffic_data.db

# Security
ENCRYPTION_KEY=your_32_character_encryption_key_here
JWT_SECRET=your_jwt_secret_key_here

# Monitoring (Optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
ALERT_EMAIL=<EMAIL>

# Performance Settings
MAX_CONCURRENT_SESSIONS=10
MAX_REQUESTS_PER_MINUTE=120

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/high_volume.log

# Development
DEBUG=false
ENVIRONMENT=production
"""
            with open(env_file, 'w') as f:
                f.write(env_content)
            print("  ✅ .env file created")
        else:
            print("  ✅ .env file already exists")
        
        # Validate high-volume config exists
        config_file = self.project_root / "config_high_volume.yaml"
        if config_file.exists():
            print("  ✅ High-volume configuration file exists")
        else:
            print("  ⚠️  High-volume configuration file not found")
            print("     Please ensure config_high_volume.yaml is present")
    
    def setup_environment_variables(self):
        """Setup environment variables"""
        print("\n🔧 Setting up environment variables...")
        
        # Load .env file
        env_file = self.project_root / ".env"
        if env_file.exists():
            print("  ✅ Environment file loaded")
        else:
            print("  ⚠️  No .env file found")
        
        # Check required environment variables
        required_vars = [
            'PROXY_API_KEY',
            'DATABASE_URL',
            'ENCRYPTION_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var) or os.getenv(var) == f"your_{var.lower()}_here":
                missing_vars.append(var)
        
        if missing_vars:
            print(f"  ⚠️  Missing environment variables: {', '.join(missing_vars)}")
            print("     Please update your .env file with actual values")
        else:
            print("  ✅ All required environment variables configured")
    
    def create_directories(self):
        """Create necessary directories"""
        print("\n📁 Creating directories...")
        
        directories = [
            "logs",
            "data",
            "backups",
            "exports",
            "temp",
            "ssl"
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(exist_ok=True)
            print(f"  ✅ {directory}/ directory created")
    
    def validate_installation(self):
        """Validate installation"""
        print("\n🔍 Validating installation...")
        
        try:
            # Test imports
            import playwright
            print("  ✅ Playwright import successful")
            
            import yaml
            print("  ✅ YAML support available")
            
            import aiohttp
            print("  ✅ Async HTTP support available")
            
            import pandas
            print("  ✅ Data processing support available")
            
            # Test high-volume configuration
            config_file = self.project_root / "config_high_volume.yaml"
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config = yaml.safe_load(f)
                
                required_sections = ['target', 'traffic', 'keywords', 'distribution', 'scheduling']
                missing_sections = [s for s in required_sections if s not in config]
                
                if missing_sections:
                    print(f"  ⚠️  Missing config sections: {', '.join(missing_sections)}")
                else:
                    print("  ✅ High-volume configuration valid")
            
            # Test main application
            main_file = self.project_root / "high_volume_main.py"
            if main_file.exists():
                print("  ✅ Main application file exists")
            else:
                print("  ❌ Main application file missing")
            
        except ImportError as e:
            print(f"  ❌ Import error: {e}")
            raise
    
    def show_next_steps(self):
        """Show next steps to user"""
        print("\n🎯 Next Steps:")
        print("=" * 40)
        
        print("\n1. Configure Your Brand and Keywords:")
        print("   Edit config_high_volume.yaml:")
        print("   - Set your website URL and brand name")
        print("   - Add your brand + keyword combinations")
        print("   - Adjust traffic targets if needed")
        
        print("\n2. Set Up Proxy Service:")
        print("   Update .env file:")
        print("   - Add your proxy API key")
        print("   - Configure backup proxy if available")
        
        print("\n3. Test Configuration:")
        print("   python high_volume_main.py validate")
        
        print("\n4. Start Small Test:")
        print("   python high_volume_main.py generate --impressions 1000 --clicks 2")
        
        print("\n5. Scale to Full Volume:")
        print("   python high_volume_main.py generate --impressions 35000 --clicks 55")
        
        print("\n6. Schedule Daily Traffic:")
        print("   python high_volume_main.py schedule --daily")
        
        print("\n7. Monitor Performance:")
        print("   python high_volume_main.py monitor --real-time --duration 60")
        
        print("\n📚 Documentation:")
        print("   - Read HIGH_VOLUME_SETUP_GUIDE.md for detailed setup")
        print("   - Check README.md for general information")
        print("   - Review DEPLOYMENT_GUIDE.md for production setup")
        
        print("\n⚠️  Important Notes:")
        print("   - Always test on your own websites first")
        print("   - Start with low volumes and scale gradually")
        print("   - Monitor success rates and adjust as needed")
        print("   - Use quality proxy services for best results")

def main():
    """Main installation function"""
    installer = HighVolumeInstaller()
    installer.run_installation()

if __name__ == "__main__":
    main()
