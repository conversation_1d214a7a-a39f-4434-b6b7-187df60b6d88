"""
Impression vs Click Management System

This module implements sophisticated system to generate high impressions (30-40k)
with controlled click-through rates (50-60 clicks) maintaining realistic CTR patterns.
"""

import asyncio
import random
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, field
from loguru import logger

@dataclass
class ImpressionSession:
    """Impression-only session data"""
    session_id: str
    keyword: str
    query: str
    timestamp: datetime
    region: str
    device_type: str
    serp_position_viewed: int
    time_on_serp: float
    interactions: List[str] = field(default_factory=list)

@dataclass
class ClickSession:
    """Click session data"""
    session_id: str
    keyword: str
    query: str
    timestamp: datetime
    region: str
    device_type: str
    serp_position: int
    time_on_serp: float
    time_on_site: float
    pages_visited: int
    bounce: bool
    interactions: List[str] = field(default_factory=list)

@dataclass
class CTRMetrics:
    """Click-through rate metrics"""
    total_impressions: int
    total_clicks: int
    current_ctr: float
    target_ctr: float
    ctr_variance: float
    hourly_ctr: Dict[int, float] = field(default_factory=dict)
    keyword_ctr: Dict[str, float] = field(default_factory=dict)

class ImpressionClickManager:
    """Manages impression and click distribution for realistic CTR"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize impression click manager"""
        self.config = config
        self.traffic_config = config['traffic']
        
        # Target metrics
        self.daily_impressions = self.traffic_config['daily_impressions']
        self.daily_clicks = self.traffic_config['daily_clicks']
        self.target_ctr = self.traffic_config['target_ctr']
        self.ctr_variance = self.traffic_config['ctr_variance']
        
        # Session tracking
        self.impression_sessions: List[ImpressionSession] = []
        self.click_sessions: List[ClickSession] = []
        
        # Real-time metrics
        self.current_metrics = CTRMetrics(
            total_impressions=0,
            total_clicks=0,
            current_ctr=0.0,
            target_ctr=self.target_ctr,
            ctr_variance=self.ctr_variance
        )
        
        # CTR control parameters
        self.ctr_control = {
            'adjustment_threshold': 0.02,  # Adjust if CTR deviates by 2%
            'max_adjustment_rate': 0.1,   # Max 10% adjustment per hour
            'smoothing_window': 100,      # Calculate CTR over last 100 sessions
            'emergency_threshold': 0.05   # Emergency adjustment if CTR off by 5%
        }
        
        logger.info(f"Impression/Click manager initialized - Target: {self.daily_impressions} impressions, {self.daily_clicks} clicks (CTR: {self.target_ctr:.3f})")
    
    def should_generate_click(self, keyword: str, current_hour: int) -> bool:
        """Determine if current session should be a click based on CTR control"""
        try:
            # Calculate current CTR
            recent_sessions = self._get_recent_sessions()
            if len(recent_sessions) < 10:  # Need minimum sessions for calculation
                # Use target probability for initial sessions
                return random.random() < self.target_ctr
            
            current_ctr = self._calculate_current_ctr(recent_sessions)
            target_ctr_adjusted = self._get_adjusted_target_ctr(keyword, current_hour)
            
            # Determine if we need more clicks or impressions
            ctr_difference = target_ctr_adjusted - current_ctr
            
            if abs(ctr_difference) < self.ctr_control['adjustment_threshold']:
                # CTR is within acceptable range, use target probability
                click_probability = target_ctr_adjusted
            elif ctr_difference > 0:
                # CTR too low, increase click probability
                adjustment = min(ctr_difference * 2, self.ctr_control['max_adjustment_rate'])
                click_probability = target_ctr_adjusted + adjustment
            else:
                # CTR too high, decrease click probability
                adjustment = min(abs(ctr_difference) * 2, self.ctr_control['max_adjustment_rate'])
                click_probability = max(0.01, target_ctr_adjusted - adjustment)
            
            # Emergency adjustment for large deviations
            if abs(ctr_difference) > self.ctr_control['emergency_threshold']:
                if ctr_difference > 0:
                    click_probability = min(0.5, click_probability * 1.5)
                else:
                    click_probability = max(0.01, click_probability * 0.5)
            
            # Apply some randomness to avoid patterns
            variance = random.uniform(0.8, 1.2)
            final_probability = click_probability * variance
            
            return random.random() < final_probability
            
        except Exception as e:
            logger.error(f"Error determining click probability: {e}")
            return random.random() < self.target_ctr
    
    def _get_recent_sessions(self) -> List[Dict[str, Any]]:
        """Get recent sessions for CTR calculation"""
        try:
            window_size = self.ctr_control['smoothing_window']
            
            # Combine impression and click sessions
            all_sessions = []
            
            # Add recent impression sessions
            for session in self.impression_sessions[-window_size:]:
                all_sessions.append({
                    'type': 'impression',
                    'timestamp': session.timestamp,
                    'keyword': session.keyword
                })
            
            # Add recent click sessions
            for session in self.click_sessions[-window_size:]:
                all_sessions.append({
                    'type': 'click',
                    'timestamp': session.timestamp,
                    'keyword': session.keyword
                })
            
            # Sort by timestamp and return most recent
            all_sessions.sort(key=lambda x: x['timestamp'], reverse=True)
            return all_sessions[:window_size]
            
        except Exception as e:
            logger.debug(f"Error getting recent sessions: {e}")
            return []
    
    def _calculate_current_ctr(self, sessions: List[Dict[str, Any]]) -> float:
        """Calculate current CTR from recent sessions"""
        try:
            if not sessions:
                return self.target_ctr
            
            clicks = sum(1 for s in sessions if s['type'] == 'click')
            total = len(sessions)
            
            return clicks / total if total > 0 else self.target_ctr
            
        except Exception as e:
            logger.debug(f"Error calculating current CTR: {e}")
            return self.target_ctr
    
    def _get_adjusted_target_ctr(self, keyword: str, current_hour: int) -> float:
        """Get adjusted target CTR based on keyword and time"""
        try:
            base_ctr = self.target_ctr
            
            # Adjust based on keyword type (from brand keyword engine)
            keyword_adjustments = {
                'brand_primary': 1.3,    # Higher CTR for brand primary
                'brand_secondary': 1.0,  # Normal CTR
                'brand_longtail': 0.8,   # Lower CTR for longtail
                'natural_keywords': 0.5  # Much lower CTR for natural
            }
            
            # Determine keyword type (simplified)
            if any(brand_term in keyword.lower() for brand_term in [self.config['target']['brand_name'].lower()]):
                if any(primary_term in keyword.lower() for primary_term in ['services', 'solutions', 'agency']):
                    keyword_type = 'brand_primary'
                else:
                    keyword_type = 'brand_secondary'
            else:
                keyword_type = 'natural_keywords'
            
            keyword_multiplier = keyword_adjustments.get(keyword_type, 1.0)
            
            # Adjust based on time of day (people more likely to click during work hours)
            time_adjustments = {
                range(0, 6): 0.7,    # Late night/early morning - lower CTR
                range(6, 9): 0.9,    # Morning - moderate CTR
                range(9, 12): 1.2,   # Morning work hours - higher CTR
                range(12, 14): 1.0,  # Lunch time - normal CTR
                range(14, 17): 1.3,  # Afternoon work hours - highest CTR
                range(17, 20): 1.1,  # Evening - moderate-high CTR
                range(20, 24): 0.8   # Night - lower CTR
            }
            
            time_multiplier = 1.0
            for time_range, multiplier in time_adjustments.items():
                if current_hour in time_range:
                    time_multiplier = multiplier
                    break
            
            # Apply adjustments with some variance
            adjusted_ctr = base_ctr * keyword_multiplier * time_multiplier
            variance = random.uniform(1 - self.ctr_variance, 1 + self.ctr_variance)
            
            return adjusted_ctr * variance
            
        except Exception as e:
            logger.debug(f"Error adjusting target CTR: {e}")
            return self.target_ctr
    
    async def execute_impression_session(self, session_config: Dict[str, Any]) -> ImpressionSession:
        """Execute impression-only session"""
        try:
            session_id = f"imp_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{random.randint(1000, 9999)}"
            
            # Import here to avoid circular imports
            from google_search_engine import GoogleSearchEngine
            from browser_manager import BrowserManager
            from proxy_manager import ProxyManager
            from fingerprint_generator import FingerprintGenerator
            
            # Initialize components
            proxy_manager = ProxyManager()
            fingerprint_generator = FingerprintGenerator()
            google_engine = GoogleSearchEngine(self.config)
            
            # Get proxy and fingerprint
            proxy = await proxy_manager.get_proxy(
                region=session_config['region'],
                proxy_type=session_config.get('proxy_type', 'datacenter')
            )
            
            fingerprint = fingerprint_generator.generate_comprehensive_fingerprint(
                region=session_config['region'],
                device_preference=session_config['device_type']
            )
            
            # Execute browser session
            async with BrowserManager() as browser_manager:
                proxy_config = {
                    'protocol': proxy.protocol,
                    'host': proxy.host,
                    'port': proxy.port,
                    'username': proxy.username,
                    'password': proxy.password
                }
                
                browser = await browser_manager.create_stealth_browser(proxy_config, headless=True)
                context = await browser_manager.create_stealth_context(fingerprint, fingerprint['locale'])
                page = await browser_manager.create_stealth_page(context)
                
                # Execute Google search session (impression only)
                search_result = await google_engine.execute_search_session(
                    page=page,
                    keyword=session_config['keyword'],
                    target_url=session_config['target_url'],
                    session_type="impression"
                )
                
                # Create impression session record
                impression_session = ImpressionSession(
                    session_id=session_id,
                    keyword=session_config['keyword'],
                    query=search_result.get('actual_query', session_config['keyword']),
                    timestamp=datetime.now(),
                    region=session_config['region'],
                    device_type=session_config['device_type'],
                    serp_position_viewed=search_result.get('target_position', 0),
                    time_on_serp=search_result.get('time_on_serp', 0),
                    interactions=search_result.get('serp_interactions', [])
                )
                
                # Update metrics
                self._update_impression_metrics(impression_session)
                
                # Store session
                self.impression_sessions.append(impression_session)
                
                logger.debug(f"Impression session completed: {session_id}")
                return impression_session
                
        except Exception as e:
            logger.error(f"Error in impression session: {e}")
            # Return minimal session record
            return ImpressionSession(
                session_id=session_id,
                keyword=session_config['keyword'],
                query=session_config['keyword'],
                timestamp=datetime.now(),
                region=session_config['region'],
                device_type=session_config['device_type'],
                serp_position_viewed=0,
                time_on_serp=0
            )
    
    async def execute_click_session(self, session_config: Dict[str, Any]) -> ClickSession:
        """Execute click session with website visit"""
        try:
            session_id = f"clk_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{random.randint(1000, 9999)}"
            
            # Import here to avoid circular imports
            from google_search_engine import GoogleSearchEngine
            from browser_manager import BrowserManager
            from proxy_manager import ProxyManager
            from fingerprint_generator import FingerprintGenerator
            
            # Initialize components
            proxy_manager = ProxyManager()
            fingerprint_generator = FingerprintGenerator()
            google_engine = GoogleSearchEngine(self.config)
            
            # Get proxy and fingerprint
            proxy = await proxy_manager.get_proxy(
                region=session_config['region'],
                proxy_type=session_config.get('proxy_type', 'datacenter')
            )
            
            fingerprint = fingerprint_generator.generate_comprehensive_fingerprint(
                region=session_config['region'],
                device_preference=session_config['device_type']
            )
            
            # Execute browser session
            async with BrowserManager() as browser_manager:
                proxy_config = {
                    'protocol': proxy.protocol,
                    'host': proxy.host,
                    'port': proxy.port,
                    'username': proxy.username,
                    'password': proxy.password
                }
                
                browser = await browser_manager.create_stealth_browser(proxy_config, headless=True)
                context = await browser_manager.create_stealth_context(fingerprint, fingerprint['locale'])
                page = await browser_manager.create_stealth_page(context)
                
                # Execute Google search session (with click)
                search_result = await google_engine.execute_search_session(
                    page=page,
                    keyword=session_config['keyword'],
                    target_url=session_config['target_url'],
                    session_type="click"
                )
                
                # Create click session record
                click_session = ClickSession(
                    session_id=session_id,
                    keyword=session_config['keyword'],
                    query=search_result.get('actual_query', session_config['keyword']),
                    timestamp=datetime.now(),
                    region=session_config['region'],
                    device_type=session_config['device_type'],
                    serp_position=search_result.get('target_position', 0),
                    time_on_serp=search_result.get('time_on_serp', 0),
                    time_on_site=search_result.get('time_on_site', 0),
                    pages_visited=search_result.get('pages_visited', 1),
                    bounce=search_result.get('bounce', True),
                    interactions=search_result.get('serp_interactions', [])
                )
                
                # Update metrics
                self._update_click_metrics(click_session)
                
                # Store session
                self.click_sessions.append(click_session)
                
                logger.debug(f"Click session completed: {session_id}")
                return click_session
                
        except Exception as e:
            logger.error(f"Error in click session: {e}")
            # Return minimal session record
            return ClickSession(
                session_id=session_id,
                keyword=session_config['keyword'],
                query=session_config['keyword'],
                timestamp=datetime.now(),
                region=session_config['region'],
                device_type=session_config['device_type'],
                serp_position=0,
                time_on_serp=0,
                time_on_site=0,
                pages_visited=1,
                bounce=True
            )
    
    def _update_impression_metrics(self, session: ImpressionSession):
        """Update impression metrics"""
        try:
            self.current_metrics.total_impressions += 1
            
            # Update hourly CTR
            hour = session.timestamp.hour
            if hour not in self.current_metrics.hourly_ctr:
                self.current_metrics.hourly_ctr[hour] = 0.0
            
            # Update keyword CTR
            if session.keyword not in self.current_metrics.keyword_ctr:
                self.current_metrics.keyword_ctr[session.keyword] = 0.0
            
            # Recalculate current CTR
            self._recalculate_current_ctr()
            
        except Exception as e:
            logger.debug(f"Error updating impression metrics: {e}")
    
    def _update_click_metrics(self, session: ClickSession):
        """Update click metrics"""
        try:
            self.current_metrics.total_clicks += 1
            
            # Update hourly CTR
            hour = session.timestamp.hour
            if hour not in self.current_metrics.hourly_ctr:
                self.current_metrics.hourly_ctr[hour] = 0.0
            
            # Update keyword CTR
            if session.keyword not in self.current_metrics.keyword_ctr:
                self.current_metrics.keyword_ctr[session.keyword] = 0.0
            
            # Recalculate current CTR
            self._recalculate_current_ctr()
            
        except Exception as e:
            logger.debug(f"Error updating click metrics: {e}")
    
    def _recalculate_current_ctr(self):
        """Recalculate current CTR"""
        try:
            total_sessions = self.current_metrics.total_impressions + self.current_metrics.total_clicks
            if total_sessions > 0:
                self.current_metrics.current_ctr = self.current_metrics.total_clicks / total_sessions
            else:
                self.current_metrics.current_ctr = 0.0
                
        except Exception as e:
            logger.debug(f"Error recalculating CTR: {e}")
    
    def get_ctr_metrics(self) -> Dict[str, Any]:
        """Get comprehensive CTR metrics"""
        return {
            'current_metrics': {
                'total_impressions': self.current_metrics.total_impressions,
                'total_clicks': self.current_metrics.total_clicks,
                'current_ctr': self.current_metrics.current_ctr,
                'target_ctr': self.current_metrics.target_ctr,
                'ctr_variance': self.current_metrics.ctr_variance
            },
            'daily_targets': {
                'target_impressions': self.daily_impressions,
                'target_clicks': self.daily_clicks,
                'progress_impressions': self.current_metrics.total_impressions / self.daily_impressions,
                'progress_clicks': self.current_metrics.total_clicks / self.daily_clicks
            },
            'hourly_distribution': self.current_metrics.hourly_ctr,
            'keyword_performance': self.current_metrics.keyword_ctr,
            'quality_metrics': {
                'avg_time_on_serp': self._calculate_avg_time_on_serp(),
                'avg_time_on_site': self._calculate_avg_time_on_site(),
                'bounce_rate': self._calculate_bounce_rate()
            }
        }
    
    def _calculate_avg_time_on_serp(self) -> float:
        """Calculate average time on SERP"""
        try:
            all_times = [s.time_on_serp for s in self.impression_sessions] + \
                       [s.time_on_serp for s in self.click_sessions]
            return sum(all_times) / len(all_times) if all_times else 0.0
        except:
            return 0.0
    
    def _calculate_avg_time_on_site(self) -> float:
        """Calculate average time on site for clicks"""
        try:
            times = [s.time_on_site for s in self.click_sessions if s.time_on_site > 0]
            return sum(times) / len(times) if times else 0.0
        except:
            return 0.0
    
    def _calculate_bounce_rate(self) -> float:
        """Calculate bounce rate for clicks"""
        try:
            if not self.click_sessions:
                return 0.0
            bounces = sum(1 for s in self.click_sessions if s.bounce)
            return bounces / len(self.click_sessions)
        except:
            return 0.0
    
    def reset_daily_metrics(self):
        """Reset metrics for new day"""
        try:
            self.impression_sessions.clear()
            self.click_sessions.clear()
            
            self.current_metrics = CTRMetrics(
                total_impressions=0,
                total_clicks=0,
                current_ctr=0.0,
                target_ctr=self.target_ctr,
                ctr_variance=self.ctr_variance
            )
            
            logger.info("Daily metrics reset")
            
        except Exception as e:
            logger.error(f"Error resetting daily metrics: {e}")
