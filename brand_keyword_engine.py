"""
Brand + Keyword Strategy Engine

This module creates intelligent brand+keyword combination system with natural
search query variations that perfectly mimic how real users search for brands.
"""

import random
import re
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from loguru import logger

@dataclass
class BrandKeywordStrategy:
    """Brand keyword strategy configuration"""
    brand_name: str
    primary_keywords: List[str]
    secondary_keywords: List[str]
    longtail_keywords: List[str]
    natural_keywords: List[str]
    
@dataclass
class SearchQuery:
    """Generated search query with metadata"""
    query: str
    keyword_type: str  # brand_primary, brand_secondary, etc.
    intent: str        # informational, navigational, commercial
    expected_ctr: float # Expected click-through rate
    session_type: str  # impression, click

class BrandKeywordEngine:
    """Intelligent brand + keyword strategy engine"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize brand keyword engine"""
        self.config = config
        self.brand_name = config['target']['brand_name']
        
        # Load keyword strategies
        self.strategies = self._load_keyword_strategies()
        
        # Search intent patterns
        self.intent_patterns = {
            'navigational': {
                'patterns': [
                    '{brand}',
                    '{brand} website',
                    '{brand} official site',
                    '{brand} login',
                    '{brand} contact',
                    'www {brand}',
                    '{brand} homepage'
                ],
                'ctr': 0.25  # High CTR for navigational
            },
            'informational': {
                'patterns': [
                    'what is {brand}',
                    '{brand} reviews',
                    '{brand} about',
                    '{brand} information',
                    'learn about {brand}',
                    '{brand} company info',
                    '{brand} background'
                ],
                'ctr': 0.12  # Medium CTR for informational
            },
            'commercial': {
                'patterns': [
                    '{brand} services',
                    '{brand} pricing',
                    '{brand} packages',
                    '{brand} solutions',
                    'hire {brand}',
                    '{brand} consultation',
                    'work with {brand}'
                ],
                'ctr': 0.18  # Medium-high CTR for commercial
            },
            'comparison': {
                'patterns': [
                    '{brand} vs',
                    '{brand} compared to',
                    '{brand} alternatives',
                    'better than {brand}',
                    '{brand} competitors',
                    'why choose {brand}',
                    '{brand} or'
                ],
                'ctr': 0.08  # Lower CTR for comparison
            }
        }
        
        # Natural search variations
        self.natural_variations = {
            'question_words': ['what', 'how', 'why', 'where', 'when', 'who'],
            'modifiers': ['best', 'top', 'good', 'reliable', 'professional', 'expert'],
            'location_modifiers': ['near me', 'in my area', 'local', 'nearby'],
            'time_modifiers': ['2024', 'latest', 'new', 'current', 'updated'],
            'quality_indicators': ['reviews', 'ratings', 'testimonials', 'feedback']
        }
        
        logger.info(f"Brand keyword engine initialized for: {self.brand_name}")
    
    def _load_keyword_strategies(self) -> BrandKeywordStrategy:
        """Load keyword strategies from configuration"""
        keywords_config = self.config['keywords']
        
        return BrandKeywordStrategy(
            brand_name=self.brand_name,
            primary_keywords=keywords_config.get('brand_primary', []),
            secondary_keywords=keywords_config.get('brand_secondary', []),
            longtail_keywords=keywords_config.get('brand_longtail', []),
            natural_keywords=keywords_config.get('natural_keywords', [])
        )
    
    def generate_search_query(self, session_type: str = "impression") -> SearchQuery:
        """Generate intelligent search query based on session type"""
        try:
            # Determine keyword type based on session type and distribution
            keyword_type = self._select_keyword_type(session_type)
            
            # Generate base query
            base_query = self._get_base_query(keyword_type)
            
            # Apply natural variations
            final_query = self._apply_natural_variations(base_query, keyword_type)
            
            # Determine search intent
            intent = self._determine_search_intent(final_query, keyword_type)
            
            # Calculate expected CTR
            expected_ctr = self._calculate_expected_ctr(intent, keyword_type)
            
            return SearchQuery(
                query=final_query,
                keyword_type=keyword_type,
                intent=intent,
                expected_ctr=expected_ctr,
                session_type=session_type
            )
            
        except Exception as e:
            logger.error(f"Error generating search query: {e}")
            # Fallback to simple brand query
            return SearchQuery(
                query=self.brand_name,
                keyword_type="brand_primary",
                intent="navigational",
                expected_ctr=0.20,
                session_type=session_type
            )
    
    def _select_keyword_type(self, session_type: str) -> str:
        """Select keyword type based on session type and distribution"""
        try:
            distribution = self.config['distribution']
            
            if session_type == "click":
                # Click sessions favor brand keywords
                click_dist = distribution['click_sessions']['keywords']
                choices = list(click_dist.keys())
                weights = list(click_dist.values())
            else:
                # Impression sessions have broader distribution
                impression_dist = distribution['impressions_only']['keywords']
                choices = list(impression_dist.keys())
                weights = list(impression_dist.values())
            
            return random.choices(choices, weights=weights)[0]
            
        except Exception as e:
            logger.debug(f"Error selecting keyword type: {e}")
            return "brand_primary"
    
    def _get_base_query(self, keyword_type: str) -> str:
        """Get base query for keyword type"""
        try:
            if keyword_type == "brand_primary":
                keywords = self.strategies.primary_keywords
            elif keyword_type == "brand_secondary":
                keywords = self.strategies.secondary_keywords
            elif keyword_type == "brand_longtail":
                keywords = self.strategies.longtail_keywords
            elif keyword_type == "natural_keywords":
                keywords = self.strategies.natural_keywords
            else:
                keywords = self.strategies.primary_keywords
            
            if not keywords:
                return self.brand_name
            
            base_keyword = random.choice(keywords)
            
            # Replace {brand} placeholder with actual brand name
            return base_keyword.replace('{brand}', self.brand_name)
            
        except Exception as e:
            logger.debug(f"Error getting base query: {e}")
            return self.brand_name
    
    def _apply_natural_variations(self, base_query: str, keyword_type: str) -> str:
        """Apply natural variations to make queries more realistic"""
        try:
            query = base_query
            
            # Apply different variation strategies based on keyword type
            if keyword_type == "natural_keywords":
                # Natural keywords get more variations
                query = self._apply_heavy_variations(query)
            elif keyword_type == "brand_primary":
                # Brand primary gets moderate variations
                query = self._apply_moderate_variations(query)
            elif keyword_type in ["brand_secondary", "brand_longtail"]:
                # Secondary and longtail get light variations
                query = self._apply_light_variations(query)
            
            return query.strip()
            
        except Exception as e:
            logger.debug(f"Error applying variations: {e}")
            return base_query
    
    def _apply_heavy_variations(self, query: str) -> str:
        """Apply heavy variations for natural keywords"""
        variations = []
        
        # 30% chance to add question words
        if random.random() < 0.30:
            question = random.choice(self.natural_variations['question_words'])
            if question in ['what', 'how']:
                query = f"{question} is {query}"
            else:
                query = f"{question} {query}"
        
        # 40% chance to add modifiers
        if random.random() < 0.40:
            modifier = random.choice(self.natural_variations['modifiers'])
            query = f"{modifier} {query}"
        
        # 25% chance to add location modifiers
        if random.random() < 0.25:
            location = random.choice(self.natural_variations['location_modifiers'])
            query = f"{query} {location}"
        
        # 15% chance to add time modifiers
        if random.random() < 0.15:
            time_mod = random.choice(self.natural_variations['time_modifiers'])
            query = f"{query} {time_mod}"
        
        # 20% chance to add quality indicators
        if random.random() < 0.20:
            quality = random.choice(self.natural_variations['quality_indicators'])
            query = f"{query} {quality}"
        
        return query
    
    def _apply_moderate_variations(self, query: str) -> str:
        """Apply moderate variations for brand primary keywords"""
        # 15% chance to add modifiers
        if random.random() < 0.15:
            modifier = random.choice(self.natural_variations['modifiers'])
            query = f"{modifier} {query}"
        
        # 20% chance to add location modifiers
        if random.random() < 0.20:
            location = random.choice(self.natural_variations['location_modifiers'])
            query = f"{query} {location}"
        
        # 10% chance to add time modifiers
        if random.random() < 0.10:
            time_mod = random.choice(self.natural_variations['time_modifiers'])
            query = f"{query} {time_mod}"
        
        return query
    
    def _apply_light_variations(self, query: str) -> str:
        """Apply light variations for brand secondary/longtail"""
        # 10% chance to add modifiers
        if random.random() < 0.10:
            modifier = random.choice(['best', 'top', 'good'])
            query = f"{modifier} {query}"
        
        # 15% chance to add location modifiers
        if random.random() < 0.15:
            location = random.choice(['near me', 'local'])
            query = f"{query} {location}"
        
        return query
    
    def _determine_search_intent(self, query: str, keyword_type: str) -> str:
        """Determine search intent based on query and keyword type"""
        try:
            query_lower = query.lower()
            
            # Check for navigational intent
            navigational_indicators = [
                'website', 'site', 'homepage', 'login', 'contact',
                'official', 'www', self.brand_name.lower()
            ]
            
            if any(indicator in query_lower for indicator in navigational_indicators):
                if keyword_type.startswith('brand_'):
                    return 'navigational'
            
            # Check for informational intent
            informational_indicators = [
                'what', 'how', 'why', 'about', 'information', 'learn',
                'reviews', 'background', 'company info'
            ]
            
            if any(indicator in query_lower for indicator in informational_indicators):
                return 'informational'
            
            # Check for comparison intent
            comparison_indicators = [
                'vs', 'compared', 'alternatives', 'better than',
                'competitors', 'or', 'versus'
            ]
            
            if any(indicator in query_lower for indicator in comparison_indicators):
                return 'comparison'
            
            # Check for commercial intent
            commercial_indicators = [
                'services', 'pricing', 'packages', 'solutions',
                'hire', 'consultation', 'work with', 'buy', 'purchase'
            ]
            
            if any(indicator in query_lower for indicator in commercial_indicators):
                return 'commercial'
            
            # Default based on keyword type
            if keyword_type == 'brand_primary':
                return 'navigational'
            elif keyword_type == 'brand_secondary':
                return 'commercial'
            elif keyword_type == 'brand_longtail':
                return 'informational'
            else:
                return 'informational'
                
        except Exception as e:
            logger.debug(f"Error determining intent: {e}")
            return 'informational'
    
    def _calculate_expected_ctr(self, intent: str, keyword_type: str) -> float:
        """Calculate expected click-through rate"""
        try:
            # Base CTR from intent
            base_ctr = self.intent_patterns.get(intent, {}).get('ctr', 0.10)
            
            # Adjust based on keyword type
            if keyword_type == 'brand_primary':
                multiplier = 1.2  # Higher CTR for brand primary
            elif keyword_type == 'brand_secondary':
                multiplier = 1.0  # Normal CTR
            elif keyword_type == 'brand_longtail':
                multiplier = 0.8  # Lower CTR for longtail
            elif keyword_type == 'natural_keywords':
                multiplier = 0.6  # Much lower CTR for natural keywords
            else:
                multiplier = 1.0
            
            # Apply some randomness
            variance = random.uniform(0.8, 1.2)
            
            final_ctr = base_ctr * multiplier * variance
            
            # Ensure CTR is within reasonable bounds
            return max(0.05, min(0.30, final_ctr))
            
        except Exception as e:
            logger.debug(f"Error calculating CTR: {e}")
            return 0.15
    
    def generate_query_batch(self, batch_size: int, 
                           impression_ratio: float = 0.9984) -> List[SearchQuery]:
        """Generate batch of search queries with proper impression/click ratio"""
        try:
            queries = []
            
            # Calculate number of impression vs click sessions
            num_impressions = int(batch_size * impression_ratio)
            num_clicks = batch_size - num_impressions
            
            # Generate impression queries
            for _ in range(num_impressions):
                query = self.generate_search_query("impression")
                queries.append(query)
            
            # Generate click queries
            for _ in range(num_clicks):
                query = self.generate_search_query("click")
                queries.append(query)
            
            # Shuffle to randomize order
            random.shuffle(queries)
            
            logger.debug(f"Generated batch: {num_impressions} impressions, {num_clicks} clicks")
            return queries
            
        except Exception as e:
            logger.error(f"Error generating query batch: {e}")
            return []
    
    def get_keyword_statistics(self) -> Dict[str, Any]:
        """Get keyword strategy statistics"""
        return {
            'brand_name': self.brand_name,
            'keyword_counts': {
                'brand_primary': len(self.strategies.primary_keywords),
                'brand_secondary': len(self.strategies.secondary_keywords),
                'brand_longtail': len(self.strategies.longtail_keywords),
                'natural_keywords': len(self.strategies.natural_keywords)
            },
            'total_keywords': (
                len(self.strategies.primary_keywords) +
                len(self.strategies.secondary_keywords) +
                len(self.strategies.longtail_keywords) +
                len(self.strategies.natural_keywords)
            ),
            'intent_patterns': list(self.intent_patterns.keys()),
            'variation_types': list(self.natural_variations.keys())
        }
    
    def validate_keyword_strategy(self) -> Dict[str, Any]:
        """Validate keyword strategy configuration"""
        validation = {
            'valid': True,
            'warnings': [],
            'errors': []
        }
        
        try:
            # Check if brand name is set
            if not self.brand_name or self.brand_name == "Your Brand":
                validation['errors'].append("Brand name not configured")
                validation['valid'] = False
            
            # Check keyword counts
            total_brand_keywords = (
                len(self.strategies.primary_keywords) +
                len(self.strategies.secondary_keywords) +
                len(self.strategies.longtail_keywords)
            )
            
            if total_brand_keywords < 5:
                validation['warnings'].append("Less than 5 brand keywords configured")
            
            if len(self.strategies.natural_keywords) < 3:
                validation['warnings'].append("Less than 3 natural keywords configured")
            
            # Check for {brand} placeholders
            all_keywords = (
                self.strategies.primary_keywords +
                self.strategies.secondary_keywords +
                self.strategies.longtail_keywords
            )
            
            brand_placeholder_count = sum(1 for kw in all_keywords if '{brand}' in kw)
            if brand_placeholder_count < len(all_keywords) * 0.5:
                validation['warnings'].append("Less than 50% of brand keywords use {brand} placeholder")
            
            return validation
            
        except Exception as e:
            validation['valid'] = False
            validation['errors'].append(f"Validation error: {str(e)}")
            return validation
