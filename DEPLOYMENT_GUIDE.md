# Advanced Organic Traffic Generation System - Deployment Guide

This guide provides step-by-step instructions for deploying the Advanced Organic Traffic Generation System in production environments.

## 🎯 Pre-Deployment Checklist

### System Requirements
- [ ] Python 3.8+ installed
- [ ] 4GB+ RAM available
- [ ] 10GB+ disk space
- [ ] Stable internet connection (100+ Mbps recommended)
- [ ] Valid proxy service subscription
- [ ] Target website access permissions

### Security Requirements
- [ ] Secure environment variables storage
- [ ] Network security configured
- [ ] Access controls implemented
- [ ] Monitoring systems in place
- [ ] Backup procedures established

## 🚀 Production Deployment

### Step 1: Environment Setup

#### 1.1 Create Production User
```bash
# Create dedicated user for the application
sudo useradd -m -s /bin/bash trafficgen
sudo usermod -aG sudo trafficgen
su - trafficgen
```

#### 1.2 Install System Dependencies
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install -y python3.8 python3.8-venv python3.8-dev
sudo apt install -y build-essential curl wget git

# Install Node.js for Playwright
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
```

#### 1.3 Setup Application Directory
```bash
# Create application directory
sudo mkdir -p /opt/trafficgen
sudo chown trafficgen:trafficgen /opt/trafficgen
cd /opt/trafficgen

# Clone repository
git clone <repository-url> .
```

### Step 2: Python Environment

#### 2.1 Create Virtual Environment
```bash
python3.8 -m venv venv
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip setuptools wheel
```

#### 2.2 Install Dependencies
```bash
# Install Python packages
pip install -r requirements.txt

# Install Playwright browsers
playwright install chromium
playwright install-deps
```

### Step 3: Configuration

#### 3.1 Environment Variables
```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

**Required Environment Variables:**
```bash
# Production environment
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# Proxy configuration
PROXY_API_KEY=your_production_proxy_api_key
BACKUP_PROXY_API_KEY=your_backup_proxy_api_key

# Database configuration
DATABASE_URL=postgresql://user:password@localhost:5432/trafficgen

# Security
ENCRYPTION_KEY=your_32_character_encryption_key
JWT_SECRET=your_jwt_secret_key

# Monitoring
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
ALERT_EMAIL=<EMAIL>

# Performance
MAX_CONCURRENT_SESSIONS=10
MAX_REQUESTS_PER_MINUTE=120
```

#### 3.2 Application Configuration
```bash
# Copy configuration template
cp config.yaml.example config.yaml

# Edit configuration
nano config.yaml
```

**Production Configuration Example:**
```yaml
target:
  url: "https://your-production-website.com"
  domain: "your-production-website.com"

keywords:
  primary:
    - "your primary keyword"
    - "main service keyword"
  secondary:
    - "secondary keyword"
    - "related service"
  longtail:
    - "specific long tail keyword phrase"

traffic:
  daily_volume: 2000
  clicks_per_keyword:
    min: 15
    max: 35

regions:
  primary:
    - "US"
    - "CA"
    - "UK"
  secondary:
    - "AU"
    - "DE"

scheduling:
  operating_hours:
    start: 8
    end: 22
  sessions_per_day: [10, 15]
  rate_limiting:
    max_concurrent: 8
    delay_between_sessions: [60, 180]

compliance:
  respect_robots_txt: true
  request_rate_limit: 2
  safety:
    max_daily_requests: 5000
    emergency_stop_threshold: 0.8
```

### Step 4: Database Setup

#### 4.1 Install PostgreSQL
```bash
sudo apt install -y postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### 4.2 Create Database
```bash
sudo -u postgres psql
```

```sql
CREATE DATABASE trafficgen;
CREATE USER trafficgen WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE trafficgen TO trafficgen;
\q
```

#### 4.3 Initialize Database
```bash
# Run database initialization
python -c "from analytics_logger import AnalyticsLogger; AnalyticsLogger()"
```

### Step 5: System Services

#### 5.1 Create Systemd Service
```bash
sudo nano /etc/systemd/system/trafficgen.service
```

```ini
[Unit]
Description=Advanced Traffic Generation System
After=network.target postgresql.service
Requires=postgresql.service

[Service]
Type=simple
User=trafficgen
Group=trafficgen
WorkingDirectory=/opt/trafficgen
Environment=PATH=/opt/trafficgen/venv/bin
ExecStart=/opt/trafficgen/venv/bin/python main.py schedule --daily
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/trafficgen

[Install]
WantedBy=multi-user.target
```

#### 5.2 Enable and Start Service
```bash
sudo systemctl daemon-reload
sudo systemctl enable trafficgen
sudo systemctl start trafficgen
```

### Step 6: Monitoring Setup

#### 6.1 Log Management
```bash
# Create log directory
sudo mkdir -p /var/log/trafficgen
sudo chown trafficgen:trafficgen /var/log/trafficgen

# Configure log rotation
sudo nano /etc/logrotate.d/trafficgen
```

```
/var/log/trafficgen/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 trafficgen trafficgen
    postrotate
        systemctl reload trafficgen
    endscript
}
```

#### 6.2 Health Check Script
```bash
nano /opt/trafficgen/healthcheck.sh
```

```bash
#!/bin/bash
cd /opt/trafficgen
source venv/bin/activate

# Run health check
python main.py status > /tmp/trafficgen_status.json

# Check if service is healthy
if [ $? -eq 0 ]; then
    echo "Service healthy"
    exit 0
else
    echo "Service unhealthy"
    exit 1
fi
```

```bash
chmod +x /opt/trafficgen/healthcheck.sh
```

#### 6.3 Monitoring Cron Jobs
```bash
crontab -e
```

```bash
# Health check every 5 minutes
*/5 * * * * /opt/trafficgen/healthcheck.sh

# Daily report at 9 AM
0 9 * * * cd /opt/trafficgen && source venv/bin/activate && python main.py analytics --export json --days 1

# Weekly cleanup at 2 AM Sunday
0 2 * * 0 cd /opt/trafficgen && source venv/bin/activate && python -c "from error_handler import ErrorHandler; ErrorHandler().cleanup_old_errors(168)"
```

## 🔒 Security Hardening

### Firewall Configuration
```bash
# Configure UFW firewall
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow from 10.0.0.0/8 to any port 5432  # Database access
```

### File Permissions
```bash
# Set secure permissions
chmod 600 /opt/trafficgen/.env
chmod 644 /opt/trafficgen/config.yaml
chmod -R 755 /opt/trafficgen/
chown -R trafficgen:trafficgen /opt/trafficgen/
```

### SSL/TLS Configuration
```bash
# Generate SSL certificates for internal communication
openssl req -x509 -newkey rsa:4096 -keyout /opt/trafficgen/ssl/key.pem -out /opt/trafficgen/ssl/cert.pem -days 365 -nodes
```

## 📊 Production Monitoring

### System Metrics
```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Monitor system resources
htop
iotop
nethogs
```

### Application Monitoring
```bash
# Check service status
sudo systemctl status trafficgen

# View logs
sudo journalctl -u trafficgen -f

# Monitor application metrics
python main.py monitor --duration 60
```

### Database Monitoring
```bash
# Monitor database performance
sudo -u postgres psql -d trafficgen -c "SELECT * FROM pg_stat_activity;"

# Check database size
sudo -u postgres psql -d trafficgen -c "SELECT pg_size_pretty(pg_database_size('trafficgen'));"
```

## 🚨 Alerting Setup

### Email Alerts
```bash
# Install mail utilities
sudo apt install -y mailutils

# Configure email alerts in application
export SMTP_HOST=smtp.gmail.com
export SMTP_PORT=587
export SMTP_USERNAME=<EMAIL>
export SMTP_PASSWORD=your_app_password
export ALERT_EMAIL=<EMAIL>
```

### Slack Integration
```bash
# Configure Slack webhook in .env
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

## 🔄 Backup and Recovery

### Database Backup
```bash
# Create backup script
nano /opt/trafficgen/backup.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/opt/trafficgen/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Database backup
pg_dump -h localhost -U trafficgen trafficgen > $BACKUP_DIR/db_backup_$DATE.sql

# Configuration backup
cp /opt/trafficgen/config.yaml $BACKUP_DIR/config_backup_$DATE.yaml
cp /opt/trafficgen/.env $BACKUP_DIR/env_backup_$DATE

# Compress backups older than 7 days
find $BACKUP_DIR -name "*.sql" -mtime +7 -exec gzip {} \;

# Remove backups older than 30 days
find $BACKUP_DIR -name "*backup*" -mtime +30 -delete
```

```bash
chmod +x /opt/trafficgen/backup.sh

# Schedule daily backups
crontab -e
# Add: 0 3 * * * /opt/trafficgen/backup.sh
```

### Recovery Procedures
```bash
# Restore database from backup
psql -h localhost -U trafficgen trafficgen < /opt/trafficgen/backups/db_backup_YYYYMMDD_HHMMSS.sql

# Restore configuration
cp /opt/trafficgen/backups/config_backup_YYYYMMDD_HHMMSS.yaml /opt/trafficgen/config.yaml
```

## 🔧 Maintenance

### Regular Maintenance Tasks
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Update Python packages
cd /opt/trafficgen
source venv/bin/activate
pip list --outdated
pip install --upgrade package_name

# Clean up logs
sudo logrotate -f /etc/logrotate.d/trafficgen

# Restart service
sudo systemctl restart trafficgen
```

### Performance Tuning
```bash
# Monitor resource usage
top
free -h
df -h

# Adjust configuration based on performance
nano /opt/trafficgen/config.yaml

# Restart service after changes
sudo systemctl restart trafficgen
```

## 🚨 Troubleshooting

### Common Issues

#### Service Won't Start
```bash
# Check service status
sudo systemctl status trafficgen

# Check logs
sudo journalctl -u trafficgen -n 50

# Validate configuration
cd /opt/trafficgen
source venv/bin/activate
python main.py config --validate
```

#### High Resource Usage
```bash
# Check resource usage
htop
iotop

# Reduce concurrent sessions
nano /opt/trafficgen/config.yaml
# Adjust: max_concurrent: 5

# Restart service
sudo systemctl restart trafficgen
```

#### Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Test database connection
psql -h localhost -U trafficgen trafficgen

# Check database logs
sudo tail -f /var/log/postgresql/postgresql-*.log
```

## 📋 Production Checklist

### Pre-Launch
- [ ] All dependencies installed
- [ ] Configuration validated
- [ ] Database initialized
- [ ] Service configured and running
- [ ] Monitoring setup
- [ ] Backups configured
- [ ] Security hardened
- [ ] Performance tested

### Post-Launch
- [ ] Monitor system metrics
- [ ] Check application logs
- [ ] Verify traffic generation
- [ ] Test alerting systems
- [ ] Validate backups
- [ ] Document any issues
- [ ] Schedule regular maintenance

## 📞 Support

For production deployment support:
- Review logs for detailed error information
- Check system resources and performance
- Validate configuration settings
- Ensure all dependencies are properly installed
- Monitor network connectivity and proxy health
