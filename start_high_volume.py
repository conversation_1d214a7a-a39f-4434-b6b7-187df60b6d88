#!/usr/bin/env python3
"""
High-Volume Traffic Generation - Quick Start Script

This script provides a simple interface to start generating high-volume traffic
with sensible defaults and guided setup.
"""

import asyncio
import sys
import os
from pathlib import Path
import yaml
from datetime import datetime
import argparse

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from high_volume_main import HighVolumeTrafficSystem
    from loguru import logger
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Please run: python install.py")
    sys.exit(1)

class QuickStart:
    """Quick start interface for high-volume traffic generation"""
    
    def __init__(self):
        """Initialize quick start"""
        self.project_root = Path.cwd()
        self.config_file = self.project_root / "config_high_volume.yaml"
        
        print("🚀 High-Volume Organic Traffic Generation - Quick Start")
        print("=" * 60)
    
    async def run_interactive_setup(self):
        """Run interactive setup wizard"""
        print("\n🎯 Interactive Setup Wizard")
        print("-" * 30)
        
        # Check if configuration exists
        if not self.config_file.exists():
            print("❌ Configuration file not found!")
            print("💡 Please copy config_example.yaml to config_high_volume.yaml")
            print("   and customize it with your brand information.")
            return False
        
        # Load configuration
        try:
            with open(self.config_file, 'r') as f:
                config = yaml.safe_load(f)
        except Exception as e:
            print(f"❌ Error loading configuration: {e}")
            return False
        
        # Check brand configuration
        brand_name = config.get('target', {}).get('brand_name', '')
        target_url = config.get('target', {}).get('url', '')
        
        if not brand_name or brand_name == "Your Brand Name":
            print("⚠️  Brand name not configured!")
            brand_name = input("Enter your brand name: ").strip()
            if not brand_name:
                print("❌ Brand name is required")
                return False
        
        if not target_url or target_url == "https://your-website.com":
            print("⚠️  Target URL not configured!")
            target_url = input("Enter your website URL: ").strip()
            if not target_url:
                print("❌ Target URL is required")
                return False
        
        # Check proxy configuration
        proxy_api_key = os.getenv('PROXY_API_KEY', '')
        if not proxy_api_key or proxy_api_key == "your_proxy_api_key_here":
            print("⚠️  Proxy API key not configured!")
            print("💡 Please set PROXY_API_KEY in your .env file")
            return False
        
        print(f"\n✅ Configuration validated:")
        print(f"   Brand: {brand_name}")
        print(f"   Website: {target_url}")
        print(f"   Proxy: Configured")
        
        return True
    
    async def run_quick_test(self):
        """Run quick test with low volume"""
        print("\n🧪 Running Quick Test (1000 impressions, 2 clicks)")
        print("-" * 50)
        
        try:
            system = HighVolumeTrafficSystem()
            
            result = await system.generate_high_volume_traffic(
                impressions=1000,
                clicks=2
            )
            
            if result['success']:
                print("✅ Quick test completed successfully!")
                print(f"   Impressions: {result['actual_impressions']}/1000")
                print(f"   Clicks: {result['actual_clicks']}/2")
                print(f"   CTR: {result['actual_ctr']:.4f}")
                print(f"   Success Rate: {result['success_rate']:.2%}")
                return True
            else:
                print(f"❌ Quick test failed: {result.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            print(f"❌ Quick test error: {e}")
            return False
    
    async def run_full_volume(self):
        """Run full volume traffic generation"""
        print("\n🚀 Starting Full Volume Traffic Generation")
        print("-" * 45)
        
        try:
            system = HighVolumeTrafficSystem()
            
            # Get targets from config
            with open(self.config_file, 'r') as f:
                config = yaml.safe_load(f)
            
            impressions = config['traffic']['daily_impressions']
            clicks = config['traffic']['daily_clicks']
            
            print(f"Target: {impressions} impressions, {clicks} clicks")
            print("This will take 18-24 hours to complete naturally...")
            
            confirm = input("\nProceed with full volume generation? (y/N): ").strip().lower()
            if confirm != 'y':
                print("❌ Cancelled by user")
                return False
            
            result = await system.generate_high_volume_traffic(
                impressions=impressions,
                clicks=clicks
            )
            
            if result['success']:
                print("✅ Full volume generation completed!")
                print(f"   Impressions: {result['actual_impressions']}/{impressions}")
                print(f"   Clicks: {result['actual_clicks']}/{clicks}")
                print(f"   CTR: {result['actual_ctr']:.4f}")
                print(f"   Success Rate: {result['success_rate']:.2%}")
                print(f"   Duration: {result['execution_time']:.1f} seconds")
                return True
            else:
                print(f"❌ Full volume generation failed: {result.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            print(f"❌ Full volume error: {e}")
            return False
    
    async def run_daily_schedule(self):
        """Run daily scheduled traffic"""
        print("\n📅 Starting Daily Scheduled Traffic")
        print("-" * 35)
        
        try:
            system = HighVolumeTrafficSystem()
            
            result = await system.schedule_daily_high_volume()
            
            if result.get('success', True):
                print("✅ Daily schedule completed!")
                print(f"   Status: {result['plan_status']}")
                print(f"   Impressions: {result['impressions_generated']}")
                print(f"   Clicks: {result['clicks_generated']}")
                print(f"   Final CTR: {result['final_ctr']:.4f}")
                print(f"   Success Rate: {result['success_rate']:.2%}")
                return True
            else:
                print(f"❌ Daily schedule failed: {result.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            print(f"❌ Daily schedule error: {e}")
            return False
    
    def show_menu(self):
        """Show main menu"""
        print("\n🎯 What would you like to do?")
        print("-" * 30)
        print("1. Run Quick Test (1000 impressions, 2 clicks)")
        print("2. Run Full Volume (35k impressions, 55 clicks)")
        print("3. Schedule Daily Traffic (distributed over 24 hours)")
        print("4. Validate Configuration")
        print("5. Show System Status")
        print("6. Exit")
        print()
        
        choice = input("Enter your choice (1-6): ").strip()
        return choice
    
    async def validate_configuration(self):
        """Validate system configuration"""
        print("\n🔍 Validating Configuration")
        print("-" * 28)
        
        try:
            system = HighVolumeTrafficSystem()
            result = system.validate_high_volume_config()
            
            print(f"Configuration: {'✅ VALID' if result['valid'] else '❌ INVALID'}")
            
            if result['errors']:
                print("\nErrors:")
                for error in result['errors']:
                    print(f"  ❌ {error}")
            
            if result['warnings']:
                print("\nWarnings:")
                for warning in result['warnings']:
                    print(f"  ⚠️  {warning}")
            
            if not result['errors'] and not result['warnings']:
                print("✅ No issues found!")
            
            return result['valid']
            
        except Exception as e:
            print(f"❌ Validation error: {e}")
            return False
    
    async def show_system_status(self):
        """Show system status"""
        print("\n📊 System Status")
        print("-" * 15)
        
        try:
            system = HighVolumeTrafficSystem()
            status = system.get_system_status()
            
            print(f"Version: {status['system_info']['version']}")
            print(f"Brand: {status['high_volume_config']['brand_name']}")
            print(f"Target URL: {status['high_volume_config']['target_url']}")
            print(f"Daily Impressions: {status['high_volume_config']['daily_impressions']:,}")
            print(f"Daily Clicks: {status['high_volume_config']['daily_clicks']}")
            print(f"Target CTR: {status['high_volume_config']['target_ctr']:.4f}")
            print(f"Keywords: {status['high_volume_config']['total_keywords']}")
            
            # Current execution status
            exec_stats = status['current_execution']
            print(f"\nCurrent Session:")
            print(f"  Impressions Generated: {exec_stats['execution_stats']['impressions_generated']}")
            print(f"  Clicks Generated: {exec_stats['execution_stats']['clicks_generated']}")
            print(f"  Success Rate: {exec_stats['success_rate']:.2%}")
            
            # CTR metrics
            ctr_metrics = status['ctr_metrics']
            print(f"  Current CTR: {ctr_metrics['current_metrics']['current_ctr']:.4f}")
            
            return True
            
        except Exception as e:
            print(f"❌ Status error: {e}")
            return False

async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="High-Volume Traffic Generation Quick Start")
    parser.add_argument('--auto-test', action='store_true', help='Run automatic test')
    parser.add_argument('--auto-full', action='store_true', help='Run full volume automatically')
    parser.add_argument('--auto-schedule', action='store_true', help='Run daily schedule automatically')
    
    args = parser.parse_args()
    
    quick_start = QuickStart()
    
    # Check setup first
    setup_ok = await quick_start.run_interactive_setup()
    if not setup_ok:
        print("\n❌ Setup incomplete. Please fix configuration and try again.")
        return 1
    
    # Handle automatic modes
    if args.auto_test:
        success = await quick_start.run_quick_test()
        return 0 if success else 1
    
    if args.auto_full:
        success = await quick_start.run_full_volume()
        return 0 if success else 1
    
    if args.auto_schedule:
        success = await quick_start.run_daily_schedule()
        return 0 if success else 1
    
    # Interactive mode
    while True:
        try:
            choice = quick_start.show_menu()
            
            if choice == '1':
                await quick_start.run_quick_test()
            elif choice == '2':
                await quick_start.run_full_volume()
            elif choice == '3':
                await quick_start.run_daily_schedule()
            elif choice == '4':
                await quick_start.validate_configuration()
            elif choice == '5':
                await quick_start.show_system_status()
            elif choice == '6':
                print("\n👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please enter 1-6.")
            
            input("\nPress Enter to continue...")
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            input("Press Enter to continue...")
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
