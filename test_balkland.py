#!/usr/bin/env python3
"""
Test Balkland SEO Traffic - Minimal Version
"""

import asyncio
import random
import aiohttp

async def test_google_search():
    """Test a single Google search"""
    
    keyword = "Balkland balkan tour"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
    }
    
    try:
        print(f"Testing Google search for: {keyword}")
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            search_url = f"https://www.google.com/search?q={keyword.replace(' ', '+')}"
            
            print(f"Searching: {search_url}")
            
            async with session.get(search_url, headers=headers) as response:
                print(f"Google response status: {response.status}")
                
                if response.status == 200:
                    content = await response.text()
                    print(f"Content length: {len(content)} characters")
                    
                    # Check if Balkland appears in results
                    if 'balkland' in content.lower():
                        print("SUCCESS: Balkland found in Google results!")
                        
                        # Test visiting Balkland.com
                        print("Testing Balkland.com visit...")
                        
                        visit_headers = headers.copy()
                        visit_headers['Referer'] = search_url
                        
                        async with session.get("https://balkland.com", headers=visit_headers) as site_response:
                            print(f"Balkland.com response status: {site_response.status}")
                            
                            if site_response.status == 200:
                                site_content = await site_response.text()
                                print(f"Balkland.com content length: {len(site_content)} characters")
                                
                                if 'balkland' in site_content.lower():
                                    print("SUCCESS: Balkland.com visit verified!")
                                    
                                    # Simulate engagement
                                    engagement_time = 30  # Short test
                                    print(f"Simulating {engagement_time}s engagement...")
                                    await asyncio.sleep(engagement_time)
                                    
                                    return {
                                        'success': True,
                                        'type': 'seo_click',
                                        'keyword': keyword,
                                        'engagement_time': engagement_time
                                    }
                                else:
                                    print("ERROR: Invalid Balkland.com content")
                            else:
                                print(f"ERROR: Balkland.com visit failed with status {site_response.status}")
                    else:
                        print("INFO: Balkland not found in Google results (impression only)")
                        return {
                            'success': True,
                            'type': 'impression',
                            'keyword': keyword
                        }
                elif response.status == 429:
                    print("WARNING: Google rate limit detected")
                    return {'success': False, 'reason': 'rate_limit'}
                else:
                    print(f"ERROR: Google search failed with status {response.status}")
                    return {'success': False, 'reason': f'google_error_{response.status}'}
                    
    except Exception as e:
        print(f"ERROR: Exception occurred - {e}")
        return {'success': False, 'reason': str(e)}

async def main():
    """Main test function"""
    print("BALKLAND SEO TRAFFIC TEST")
    print("=" * 30)
    print("Testing Google search -> Balkland.com click")
    print("=" * 30)
    
    result = await test_google_search()
    
    print("\nTEST RESULTS:")
    print(f"Success: {result.get('success', False)}")
    print(f"Type: {result.get('type', 'unknown')}")
    print(f"Keyword: {result.get('keyword', 'unknown')}")
    
    if result.get('success'):
        if result.get('type') == 'seo_click':
            print(f"Engagement Time: {result.get('engagement_time', 0)}s")
            print("\nSUCCESS: SEO click simulation completed!")
            print("The system can successfully:")
            print("1. Perform Google searches")
            print("2. Find Balkland in results")
            print("3. Click and visit Balkland.com")
            print("4. Simulate realistic engagement")
        else:
            print("\nSUCCESS: Google search impression recorded")
            print("Balkland was not in top results this time")
    else:
        print(f"Reason: {result.get('reason', 'unknown')}")
        print("\nThe test encountered an issue.")
        print("This could be due to:")
        print("- Google rate limiting")
        print("- Network connectivity")
        print("- Anti-bot measures")
    
    return result.get('success', False)

if __name__ == "__main__":
    try:
        print("Starting Balkland SEO test...")
        success = asyncio.run(main())
        
        if success:
            print("\nTEST PASSED!")
            print("Ready to run full SEO traffic generation")
        else:
            print("\nTEST FAILED!")
            print("Check network connection and try again")
            
    except KeyboardInterrupt:
        print("\nTest stopped by user")
    except Exception as e:
        print(f"\nTest error: {e}")
