{"name": "GOD Digital Marketing - Complete Social Media Automation", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 8,13,18 * * 1,2,3,4,5,6,7"}]}}, "id": "schedule-trigger", "name": "Daily Content Scheduler", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [-2400, 0]}, {"parameters": {}, "id": "manual-trigger", "name": "Manual Test Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-2400, 100]}, {"parameters": {"jsCode": "// GOD Digital Marketing - 7-Day Strategic Configuration\nconst currentDay = new Date().getDay(); // 0=Sunday, 1=Monday, etc.\nconst rotationDay = currentDay === 0 ? 7 : currentDay; // Convert Sunday to 7\n\nconst config = {\n  // Company Information\n  company_name: 'GOD Digital Marketing',\n  website: 'https://godigitalmarketing.com',\n  value_proposition: 'We Transform Businesses Through AI-Powered Digital Marketing, Automation & Development Solutions That Generate 500%+ ROI',\n  \n  // Complete Service Portfolio\n  services: {\n    digital_marketing: [\n      'Search Engine Optimization (SEO)',\n      'Pay-Per-Click Advertising (PPC/SEM)',\n      'Social Media Marketing & Management',\n      'Content Marketing & Strategy',\n      'Email Marketing & Automation',\n      'Influencer Marketing',\n      'Conversion Rate Optimization (CRO)',\n      'Marketing Analytics & Reporting',\n      'Brand Strategy & Positioning',\n      'Online Reputation Management'\n    ],\n    technology_automation: [\n      'AI Automation Solutions (ChatGPT integrations, AI chatbots)',\n      'Business Process Automation (Workflow optimization, CRM automation)',\n      'Web Development (Custom websites, landing pages, e-commerce)',\n      'App Development (Mobile applications, progressive web apps)',\n      'Marketing Automation Systems',\n      'Lead Nurturing & Scoring Systems',\n      'Customer Journey Automation',\n      'n8n Workflow Development'\n    ]\n  },\n  \n  // 7-Day Content Strategy\n  content_calendar: {\n    1: { // Monday - Educational Excellence\n      type: 'educational',\n      focus: 'Educational Excellence - Establish Authority',\n      goal: 'Establish authority and provide genuine value',\n      psychology: 'Authority & Trust Building',\n      content_types: ['In-depth tutorials', 'Case studies', 'How-to guides', 'Industry insights'],\n      cta_style: ['Save this post for later', 'Share with someone who needs this'],\n      hashtags: '#DigitalMarketing #MarketingTips #BusinessGrowth #MarketingStrategy #DigitalTransformation #MarketingEducation #GODDigitalMarketing'\n    },\n    2: { // Tuesday - Achievement Showcase\n      type: 'achievements',\n      focus: 'Achievement Showcase - Build Credibility',\n      goal: 'Build credibility through social proof',\n      psychology: 'FOMO & Aspiration',\n      content_types: ['Client results', 'Project highlights', 'Team accomplishments', 'Success metrics'],\n      cta_style: ['Ready for similar results?', 'DM us to discuss your project'],\n      hashtags: '#ClientSuccess #CaseStudy #MarketingResults #BusinessSuccess #ROI #MarketingWins #GrowthResults #GODDigitalMarketing'\n    },\n    3: { // Wednesday - Strategic Marketing Nudge\n      type: 'marketing_psychology',\n      focus: 'Strategic Marketing Nudge - Create Urgency',\n      goal: 'Create urgency and highlight pain points',\n      psychology: 'Loss Aversion & Urgency',\n      content_types: ['What you\\'re missing', 'Market insights', 'Competitor analysis', 'Problem agitation'],\n      cta_style: ['Don\\'t let competitors get ahead', 'Book a free strategy call'],\n      hashtags: '#MarketingPsychology #ConsumerBehavior #MarketingStrategy #BusinessPsychology #ConversionOptimization #GODDigitalMarketing'\n    },\n    4: { // Thursday - Industry Trends & News\n      type: 'trends_news',\n      focus: 'Industry Trends & News - Thought Leadership',\n      goal: 'Position as industry thought leader',\n      psychology: 'Authority & Insider Knowledge',\n      content_types: ['Algorithm updates', 'Emerging technologies', 'Market predictions', 'Industry analysis'],\n      cta_style: ['What\\'s your take on this?', 'Stay ahead with our insights'],\n      hashtags: '#MarketingTrends #DigitalTrends #FutureOfMarketing #MarketingInnovation #TechTrends #IndustryNews #GODDigitalMarketing'\n    },\n    5: { // Friday - Value-Driven Freebies\n      type: 'free_resources',\n      focus: 'Value-Driven Freebies - Lead Generation',\n      goal: 'Build email list and demonstrate expertise',\n      psychology: 'Reciprocity Principle',\n      content_types: ['Free resources', 'Templates', 'Checklists', 'Mini-courses', 'n8n workflows'],\n      cta_style: ['Download for free in our bio', 'Get instant access'],\n      hashtags: '#FreeResources #MarketingTools #Templates #Guides #MarketingFreebies #ValueFirst #CommunityFirst #GODDigitalMarketing'\n    },\n    6: { // Saturday - Community Engagement\n      type: 'community',\n      focus: 'Community Engagement - Foster Relationships',\n      goal: 'Foster relationships and gather insights',\n      psychology: 'Belonging & Community',\n      content_types: ['Polls', 'Q&A sessions', 'Behind-the-scenes', 'Team spotlights'],\n      cta_style: ['Tell us in the comments', 'Join the conversation'],\n      hashtags: '#CommunityFirst #MarketingCommunity #NetworkingTips #BusinessNetworking #MarketingSupport #GODDigitalMarketing'\n    },\n    7: { // Sunday - Motivational & Inspirational\n      type: 'motivational',\n      focus: 'Motivational & Inspirational - Emotional Connection',\n      goal: 'Inspire action and emotional connection',\n      psychology: 'Inspiration & Emotional Triggers',\n      content_types: ['Success stories', 'Motivational content', 'Journey stories', 'Transformation narratives'],\n      cta_style: ['Tag someone who needs to see this', 'Your journey starts today'],\n      hashtags: '#MondayMotivation #BusinessInspiration #EntrepreneurLife #SuccessStories #MarketingMotivation #GODDigitalMarketing'\n    }\n  },\n  \n  // Brand Voice\n  brand_voice: 'Professional yet approachable, results-driven, innovative, trustworthy, and community-focused. We speak with confidence backed by proven results.',\n  \n  // Current day strategy\n  todays_strategy: null\n};\n\n// Set today's strategy\nconfig.todays_strategy = config.content_calendar[rotationDay];\n\nreturn {\n  ...config,\n  rotation_day: rotationDay,\n  day_name: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][currentDay],\n  timestamp: new Date().toISOString()\n};"}, "id": "config-setup", "name": "Strategic Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2200, 50]}, {"parameters": {"jsCode": "// Strategic Content Calendar - Generate Today's Content Strategy\nconst config = $input.first().json;\nconst strategy = config.todays_strategy;\nconst rotationDay = config.rotation_day;\n\n// Content templates based on 7-day rotation\nconst contentTemplates = {\n  1: { // Monday - Educational\n    hooks: [\n      '🎓 Master digital marketing in 2024 with this complete guide',\n      '💡 The #1 marketing mistake that\\'s costing you thousands',\n      '🚀 How to 10X your marketing ROI with proven strategies',\n      '⚡ Advanced marketing tactics industry leaders use'\n    ],\n    angles: [\n      'Complete step-by-step guide to building a marketing system that generates consistent leads',\n      'Advanced automation techniques that save 20+ hours per week while increasing results by 300%',\n      'Data-driven marketing strategies used by Fortune 500 companies to dominate their markets',\n      'The ultimate digital marketing blueprint that transforms struggling businesses into market leaders'\n    ],\n    lead_magnets: [\n      'Free Digital Marketing Masterclass + Complete Strategy Template',\n      'Marketing Automation Blueprint (Worth $2,997) - Free Download',\n      'Advanced Analytics Dashboard Template + Setup Guide',\n      'Complete Marketing ROI Calculator + Optimization Checklist'\n    ]\n  },\n  2: { // Tuesday - Achievements\n    hooks: [\n      '🏆 Client Results: 847% ROI increase in just 90 days',\n      '💰 Case Study: $2.3M in revenue generated for struggling startup',\n      '📈 Transformation Story: From $10K to $100K monthly revenue',\n      '🎉 Success Alert: Another client achieves 500% lead increase'\n    ],\n    angles: [\n      'Behind-the-scenes look at our latest client transformation and the exact strategies we used',\n      'Real case study: Complete marketing overhaul that changed everything for this business',\n      'Client spotlight: How we helped a failing business become the market leader in 6 months',\n      'Success story breakdown: The 3-step system that generated $1M+ in new revenue'\n    ],\n    lead_magnets: [\n      'Free Business Growth Audit + Personalized Strategy Session',\n      'Success Story Collection: 50+ Client Transformations (PDF)',\n      'Revenue Growth Calculator + Custom Action Plan',\n      'Free Marketing Performance Analysis + Optimization Report'\n    ]\n  },\n  3: { // Wednesday - Psychology\n    hooks: [\n      '🧠 The psychology trick that increases conversions by 340%',\n      '⚠️ Your competitors are using this against you RIGHT NOW',\n      '🔥 Limited Time: The marketing secret that\\'s changing everything',\n      '💥 Why 95% of businesses fail at marketing (and how to be the 5%)'\n    ],\n    angles: [\n      'Psychological triggers that make customers buy immediately - ethical tactics that work',\n      'The hidden marketing psychology your competitors don\\'t understand (but should)',\n      'Scarcity and urgency tactics that ethical marketers use to drive massive action',\n      'Consumer behavior insights that transform marketing performance overnight'\n    ],\n    lead_magnets: [\n      'Psychology-Based Marketing Playbook (Limited 48-Hour Access)',\n      'Conversion Psychology Checklist + Implementation Templates',\n      'Exclusive: Advanced Persuasion Techniques Guide (Members Only)',\n      'Marketing Psychology Masterclass + Case Study Collection'\n    ]\n  },\n  4: { // Thursday - Trends\n    hooks: [\n      '🔮 2024 Marketing Predictions: What\\'s coming that will change everything',\n      '📊 Breaking: Algorithm changes affecting 90% of businesses',\n      '🌟 The emerging trend that will dominate marketing in 2024',\n      '⚡ Industry Alert: Major shift happening in digital marketing'\n    ],\n    angles: [\n      'Latest industry trends and what they mean for your business success',\n      'Future of digital marketing: Trends to watch and prepare for in 2024',\n      'Breaking down the latest algorithm updates and their real impact on businesses',\n      'Emerging technologies reshaping the marketing landscape - stay ahead of the curve'\n    ],\n    lead_magnets: [\n      '2024 Marketing Trends Report + Implementation Action Plan',\n      'Future-Proof Marketing Strategy Guide + Trend Analysis',\n      'Industry Trends Analysis + Competitive Advantage Roadmap',\n      'Exclusive Trend Insights + Early Adopter Advantage Guide'\n    ]\n  },\n  5: { // Friday - Free Resources\n    hooks: [\n      '🎁 FREE: Complete marketing automation workflow (Worth $5,000)',\n      '💝 Friday Freebie: Advanced n8n workflow for lead generation',\n      '🆓 Giving away our secret marketing templates (Today only)',\n      '🎉 Free Resource Friday: Tools that transformed our agency'\n    ],\n    angles: [\n      'Complete collection of marketing templates and tools that have generated millions in revenue',\n      'Advanced automation workflows you can implement today to save hours and increase results',\n      'Free resources that have transformed thousands of businesses - now available to you',\n      'Community-exclusive tools and templates for explosive business growth'\n    ],\n    lead_magnets: [\n      'Complete Marketing Toolkit (50+ Templates & Tools) - Lifetime Access',\n      'Advanced n8n Automation Workflows Collection + Setup Guide',\n      'Marketing Resource Library (100+ Assets) + VIP Community Access',\n      'Free Strategy Session + Custom Workflow Setup (Limited Spots)'\n    ]\n  },\n  6: { // Saturday - Community\n    hooks: [\n      '🤝 Community Question: What\\'s your biggest marketing challenge?',\n      '💬 Let\\'s discuss: Best marketing win you\\'ve had this week?',\n      '🎯 Poll Time: Which marketing strategy should we cover next?',\n      '👥 Behind the scenes: How our team approaches client challenges'\n    ],\n    angles: [\n      'Building our marketing community - your insights and experiences matter',\n      'Let\\'s learn from each other: Share your marketing wins and challenges',\n      'Community-driven content: What topics would help your business most?',\n      'Behind-the-scenes look at how we solve complex marketing challenges'\n    ],\n    lead_magnets: [\n      'Join Our VIP Marketing Community + Exclusive Resources',\n      'Community Member Benefits: Free Tools + Monthly Masterclasses',\n      'Exclusive Community Access + Direct Line to Our Team',\n      'VIP Community Membership + Free Monthly Strategy Sessions'\n    ]\n  },\n  7: { // Sunday - Motivational\n    hooks: [\n      '✨ Sunday Inspiration: Your marketing breakthrough is closer than you think',\n      '🌟 Success Story: From struggling entrepreneur to industry leader',\n      '💪 Monday Motivation: This week, take action on your marketing goals',\n      '🚀 Transformation Sunday: Your business growth journey starts now'\n    ],\n    angles: [\n      'Inspirational success stories that prove anything is possible with the right marketing strategy',\n      'Motivational insights from entrepreneurs who transformed their businesses through marketing',\n      'Sunday reflection: How far you\\'ve come and where you\\'re headed with your marketing',\n      'Transformation stories that inspire action and remind you why you started'\n    ],\n    lead_magnets: [\n      'Success Story Collection + Motivation Toolkit',\n      'Entrepreneur\\'s Journey Guide + Inspiration Resources',\n      'Transformation Roadmap + Success Mindset Training',\n      'Motivational Masterclass + Goal Achievement System'\n    ]\n  }\n};\n\nconst template = contentTemplates[rotationDay];\nconst selectedHook = template.hooks[Math.floor(Math.random() * template.hooks.length)];\nconst selectedAngle = template.angles[Math.floor(Math.random() * template.angles.length)];\nconst selectedMagnet = template.lead_magnets[Math.floor(Math.random() * template.lead_magnets.length)];\n\n// Generate CTAs based on content type\nconst ctas = strategy.cta_style;\nconst selectedCTA = ctas[Math.floor(Math.random() * ctas.length)];\n\nreturn {\n  content_type: strategy.type,\n  content_focus: strategy.focus,\n  content_goal: strategy.goal,\n  psychological_trigger: strategy.psychology,\n  hook: selectedHook,\n  content_angle: selectedAngle,\n  lead_magnet: selectedMagnet,\n  cta_strategy: selectedCTA,\n  hashtags: strategy.hashtags,\n  rotation_day: rotationDay,\n  day_name: config.day_name,\n  strategy_ready: true,\n  timestamp: new Date().toISOString()\n};"}, "id": "content-calendar", "name": "Strategic Content Calendar", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2000, 50]}, {"parameters": {"url": "https://trends.google.com/trends/trendingsearches/daily/rss?geo=US", "options": {}}, "id": "trends-research", "name": "Industry Trends Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-1800, 150]}, {"parameters": {"jsCode": "// Advanced Trend Analysis for Content Enhancement\nconst trendData = $input.first()?.json || {};\nconst contentStrategy = $('Strategic Content Calendar').item.json;\n\n// Extract Google Trends\nlet googleTrends = [];\ntry {\n  if (trendData.rss && trendData.rss.channel && trendData.rss.channel.item) {\n    const items = Array.isArray(trendData.rss.channel.item) ? trendData.rss.channel.item : [trendData.rss.channel.item];\n    googleTrends = items.slice(0, 8).map(item => item.title);\n  }\n} catch (error) {\n  console.log('Google trends parsing failed');\n}\n\n// Industry-specific trends\nconst digitalMarketingTrends = [\n  'AI Marketing Automation 2024', 'ChatGPT for Business', 'TikTok Marketing Strategy',\n  'Google Analytics 4', 'iOS Privacy Changes', 'Voice Search Optimization',\n  'Video Marketing Trends', 'Influencer Marketing ROI', 'Marketing Attribution',\n  'Customer Data Platforms', 'Programmatic Advertising', 'Social Commerce'\n];\n\n// Content-type specific trends\nconst contentTypeTrends = {\n  educational: ['Marketing Best Practices', 'Digital Strategy', 'ROI Optimization'],\n  achievements: ['Client Success', 'Business Transformation', 'Revenue Growth'],\n  marketing_psychology: ['Consumer Behavior', 'Conversion Psychology', 'Buyer Journey'],\n  trends_news: ['Marketing Innovation', 'Industry Disruption', 'Future Predictions'],\n  free_resources: ['Marketing Templates', 'Free Tools', 'Resource Libraries'],\n  community: ['Marketing Community', 'Networking', 'Business Support'],\n  motivational: ['Success Stories', 'Business Inspiration', 'Entrepreneur Journey']\n};\n\n// Combine trends\nconst allTrends = [\n  ...googleTrends,\n  ...digitalMarketingTrends,\n  ...(contentTypeTrends[contentStrategy.content_type] || [])\n];\n\nconst selectedTrends = allTrends.slice(0, 15);\nconst primaryKeyword = selectedTrends[0] || 'digital marketing';\n\nreturn {\n  google_trends: googleTrends,\n  digital_marketing_trends: digitalMarketingTrends,\n  content_type_trends: contentTypeTrends[contentStrategy.content_type],\n  selected_trends: selectedTrends,\n  primary_keyword: primaryKeyword,\n  content_type: contentStrategy.content_type,\n  trends_ready: true,\n  timestamp: new Date().toISOString()\n};"}, "id": "trend-analyzer", "name": "Advanced Trend Analyzer", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1600, 150]}, {"parameters": {"model": "meta-llama/llama-3.1-70b-versatile", "options": {"temperature": 0.7, "maxTokens": 4000}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-1400, 200], "id": "groq-ai-model", "name": "Advanced Groq AI Model", "credentials": {"groqApi": {"id": "YOUR_GROQ_API_CREDENTIAL_ID", "name": "Groq API"}}}, {"parameters": {"promptType": "define", "text": "=You are the world's leading AI marketing strategist for GOD Digital Marketing, specializing in creating viral, conversion-focused content that generates massive engagement and leads.\n\nTODAY'S MISSION: Create {{ $('Strategic Content Calendar').item.json.content_type }} content for {{ $('Strategic Content Calendar').item.json.day_name }}.\n\nCONTEXT:\n• Company: {{ $('Strategic Configuration').item.json.company_name }}\n• Services: {{ JSON.stringify($('Strategic Configuration').item.json.services) }}\n• Value Prop: {{ $('Strategic Configuration').item.json.value_proposition }}\n• Content Focus: {{ $('Strategic Content Calendar').item.json.content_focus }}\n• Psychological Trigger: {{ $('Strategic Content Calendar').item.json.psychological_trigger }}\n• Trending Topics: {{ $('Advanced Trend Analyzer').item.json.selected_trends }}\n\nCONTENT STRATEGY:\n• Hook: {{ $('Strategic Content Calendar').item.json.hook }}\n• Angle: {{ $('Strategic Content Calendar').item.json.content_angle }}\n• Lead Magnet: {{ $('Strategic Content Calendar').item.json.lead_magnet }}\n• CTA: {{ $('Strategic Content Calendar').item.json.cta_strategy }}\n• Hashtags: {{ $('Strategic Content Calendar').item.json.hashtags }}\n\nCREATE PROFESSIONAL CONTENT FOR ALL PLATFORMS:\n1. FACEBOOK_POST: Engaging, story-driven, community-focused (include https://godigitalmarketing.com)\n2. INSTAGRAM_CAPTION: Visual storytelling, hashtag-optimized, action-oriented\n3. INSTAGRAM_STORY: Quick, swipeable, compelling CTA\n4. LINKEDIN_POST: Professional, thought leadership, B2B focused\n5. TWITTER_THREAD: Educational, viral potential, 8-10 tweets\n6. TWITTER_SINGLE: Punchy, retweetable, trending\n7. YOUTUBE_TITLE: SEO-optimized, click-worthy, under 60 characters\n8. YOUTUBE_DESCRIPTION: Detailed, timestamp-rich, link-optimized\n9. TIKTOK_SCRIPT: Trending, educational, hook-heavy\n10. PINTEREST_TITLE: SEO-focused, keyword-rich\n11. PINTEREST_DESCRIPTION: Search-optimized, actionable\n12. REDDIT_TITLE: Community-friendly, discussion-starter\n13. REDDIT_POST: Value-first, authentic, helpful\n14. DISCORD_MESSAGE: Community-focused, engaging\n15. TELEGRAM_MESSAGE: Direct, actionable, link-optimized\n\nREQUIREMENTS:\n• Include https://godigitalmarketing.com strategically in each post\n• Use specific numbers, results, and case studies when relevant\n• Avoid AI-sounding phrases - write naturally and conversationally\n• Make each post conversion-focused with strong CTAs\n• Include relevant hashtags for each platform\n• Focus on {{ $('Strategic Content Calendar').item.json.psychological_trigger }}\n• Incorporate trending topics naturally\n• Maintain GOD Digital Marketing's professional yet approachable voice\n• Each post should drive action and build community\n• Ensure content matches the {{ $('Strategic Content Calendar').item.json.content_type }} theme\n\nFormat as JSON with all platform keys. Make it irresistible and results-driven."}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [-1400, 50], "id": "master-content-ai", "name": "Master Content Creator AI"}, {"parameters": {"jsCode": "// Ultimate Content Processor for GOD Digital Marketing\nconst aiResponse = $input.first()?.json?.response || $input.first()?.json?.output || '';\nconst strategy = $('Strategic Content Calendar').item.json;\nconst config = $('Strategic Configuration').item.json;\nconst trends = $('Advanced Trend Analyzer').item.json;\n\nlet socialContent = {};\n\n// Try to extract JSON from AI response\ntry {\n  const jsonMatch = aiResponse.match(/\\{[\\s\\S]*\\}/);\n  if (jsonMatch) {\n    socialContent = JSON.parse(jsonMatch[0]);\n  }\n} catch (error) {\n  console.log('AI content parsing failed, using premium fallback');\n}\n\n// Premium fallback content generation\nif (Object.keys(socialContent).length === 0) {\n  const hook = strategy.hook;\n  const contentAngle = strategy.content_angle;\n  const leadMagnet = strategy.lead_magnet;\n  const cta = strategy.cta_strategy;\n  const hashtags = strategy.hashtags;\n  \n  socialContent = {\n    facebook_post: `${hook}\\n\\n${contentAngle}\\n\\n🎁 EXCLUSIVE: ${leadMagnet}\\n\\n${cta}\\n\\nWhat's your experience with this? Share your thoughts below! 👇\\n\\n🔗 Learn more: https://godigitalmarketing.com\\n\\n${hashtags}`,\n    \n    instagram_caption: `${hook} ✨\\n\\n${contentAngle}\\n\\n🔥 ${leadMagnet}\\n\\n${cta}\\n\\n💭 Save this post and share with someone who needs this!\\n\\n🔗 Link in bio: https://godigitalmarketing.com\\n\\n${hashtags}`,\n    \n    instagram_story: `${hook}\\n\\n${contentAngle.substring(0, 120)}...\\n\\n🔥 ${leadMagnet}\\n\\nSwipe up to learn more! 👆`,\n    \n    linkedin_post: `${hook}\\n\\n${contentAngle}\\n\\n🎯 Key Insight: This strategy has transformed countless businesses in our portfolio.\\n\\n💡 ${leadMagnet}\\n\\n${cta}\\n\\nWhat's been your experience with this approach? Share your thoughts below.\\n\\n🔗 https://godigitalmarketing.com\\n\\n${hashtags.replace(/#/g, '')}`,\n    \n    twitter_thread: `🧵 THREAD: ${hook} (1/8)\\n\\n${contentAngle}\\n\\nHere's what we've learned from working with 500+ businesses... 👇\\n\\n2/8 ${contentAngle.split('.')[0]}...`,\n    \n    twitter_single: `${hook}\\n\\n${contentAngle.substring(0, 180)}...\\n\\n${cta}\\n\\n🔗 https://godigitalmarketing.com\\n\\n${hashtags}`,\n    \n    youtube_title: `${hook.replace(/🎓|💡|🚀|⚡|🏆|💰|📈|🎉|🧠|⚠️|🔥|💥|🔮|📊|🌟|🎁|💝|🆓|🤝|💬|🎯|👥|✨|🌟|💪/, '')} | GOD Digital Marketing`,\n    \n    youtube_description: `${contentAngle}\\n\\n🎁 ${leadMagnet}\\n\\n${cta}\\n\\n⏰ TIMESTAMPS:\\n0:00 Introduction\\n1:30 The Challenge\\n3:00 Our Solution\\n5:30 Implementation\\n7:00 Results\\n8:30 Next Steps\\n\\n🔗 USEFUL LINKS:\\n• Free Resources: https://godigitalmarketing.com\\n• Book a Strategy Call: https://godigitalmarketing.com/contact\\n• Follow Us: https://godigitalmarketing.com/social\\n\\n${hashtags}\\n\\n---\\nGOD Digital Marketing - Transforming Businesses Through AI-Powered Solutions`,\n    \n    tiktok_script: `Hook: ${hook}\\nProblem: ${contentAngle.split('.')[0]}\\nSolution: ${contentAngle.split('.').slice(1).join('.')}\\nProof: Real client results\\nCTA: ${cta}\\nLink: https://godigitalmarketing.com\\nHashtags: ${hashtags}`,\n    \n    pinterest_title: `${contentAngle.split('.')[0]} | ${leadMagnet} | GOD Digital Marketing`,\n    \n    pinterest_description: `${contentAngle}\\n\\n✅ ${leadMagnet}\\n\\n${cta}\\n\\n🔗 https://godigitalmarketing.com\\n\\n${hashtags}`,\n    \n    reddit_title: hook.replace(/🎓|💡|🚀|⚡|🏆|💰|📈|🎉|🧠|⚠️|🔥|💥|🔮|📊|🌟|🎁|💝|🆓|🤝|💬|🎯|👥|✨|🌟|💪/, '').trim(),\n    \n    reddit_post: `${contentAngle}\\n\\n**${leadMagnet}**\\n\\n${cta}\\n\\nWhat's been your experience with this? Would love to hear your thoughts and answer any questions!\\n\\nMore resources: https://godigitalmarketing.com`,\n    \n    discord_message: `🚀 **${hook}**\\n\\n${contentAngle}\\n\\n💡 **${leadMagnet}**\\n\\n${cta}\\n\\n🔗 https://godigitalmarketing.com\\n\\n@everyone What do you think about this strategy?`,\n    \n    telegram_message: `🎯 ${hook}\\n\\n${contentAngle}\\n\\n🎁 ${leadMagnet}\\n\\n${cta}\\n\\n🔗 https://godigitalmarketing.com\\n\\nJoin our community for exclusive content!`\n  };\n}\n\n// Quality Assessment\nconst qualityMetrics = {\n  content_length_check: Object.values(socialContent).every(content => content && content.length >= 50),\n  platform_coverage: Object.keys(socialContent).length >= 10,\n  cta_presence: Object.values(socialContent).every(content => \n    content && (content.includes('DM') || content.includes('Comment') || content.includes('Click') || content.includes('Visit') || content.includes('Book') || content.includes('Download'))\n  ),\n  website_link: Object.values(socialContent).every(content => \n    content && content.includes('godigitalmarketing.com')\n  ),\n  engagement_elements: Object.values(socialContent).every(content => \n    content && (content.includes('?') || content.includes('👇') || content.includes('comment') || content.includes('share'))\n  )\n};\n\nconst qualityScore = Object.values(qualityMetrics).filter(Boolean).length / Object.keys(qualityMetrics).length * 10;\n\nreturn {\n  ...socialContent,\n  quality_metrics: {\n    ...qualityMetrics,\n    overall_score: qualityScore,\n    quality_pass: qualityScore >= 8,\n    platforms_ready: Object.keys(socialContent).length\n  },\n  content_type: strategy.content_type,\n  hook: strategy.hook,\n  content_angle: strategy.content_angle,\n  lead_magnet: strategy.lead_magnet,\n  cta_strategy: strategy.cta_strategy,\n  primary_keyword: trends.primary_keyword,\n  processing_complete: true,\n  timestamp: new Date().toISOString()\n};"}, "id": "content-processor", "name": "Ultimate Content Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1200, 50]}, {"parameters": {"url": "https://api.unsplash.com/search/photos", "authentication": "predefinedCredentialType", "nodeCredentialType": "unsplashApi", "sendQuery": true, "queryParameters": {"parameters": [{"name": "query", "value": "={{ $('Ultimate Content Processor').item.json.primary_keyword }}"}, {"name": "per_page", "value": "5"}, {"name": "orientation", "value": "landscape"}, {"name": "content_filter", "value": "high"}]}, "options": {}}, "id": "image-search", "name": "Professional Image Search", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-1000, 150]}, {"parameters": {"jsCode": "// Enhanced Image Processing for All Platforms\nconst imageData = $input.first()?.json || {};\nconst contentData = $('Ultimate Content Processor').item.json;\n\nlet selectedImage = null;\n\n// Try to get image from Unsplash\nif (imageData.results && imageData.results.length > 0) {\n  selectedImage = imageData.results[0];\n}\n\n// Generate platform-optimized image URLs\nif (selectedImage) {\n  const baseUrl = selectedImage.urls.regular;\n  \n  return {\n    // Original high-quality image\n    image_url: baseUrl,\n    image_url_hd: selectedImage.urls.full,\n    \n    // Platform-optimized versions\n    facebook_image: baseUrl + '&w=1200&h=630&fit=crop',\n    instagram_image: baseUrl + '&w=1080&h=1080&fit=crop',\n    instagram_story: baseUrl + '&w=1080&h=1920&fit=crop',\n    linkedin_image: baseUrl + '&w=1200&h=627&fit=crop',\n    twitter_image: baseUrl + '&w=1200&h=675&fit=crop',\n    pinterest_image: baseUrl + '&w=1000&h=1500&fit=crop',\n    youtube_thumbnail: baseUrl + '&w=1280&h=720&fit=crop',\n    \n    // Image metadata\n    image_alt: selectedImage.alt_description || `${contentData.primary_keyword} - GOD Digital Marketing`,\n    image_credit: `Photo by ${selectedImage.user.name} on Unsplash`,\n    image_source: 'unsplash',\n    image_quality_score: 10,\n    platform_ready: true\n  };\n} else {\n  // Fallback to branded placeholder\n  return {\n    image_url: 'https://via.placeholder.com/1200x630/4F46E5/FFFFFF?text=GOD+Digital+Marketing',\n    facebook_image: 'https://via.placeholder.com/1200x630/4F46E5/FFFFFF?text=Facebook+Post',\n    instagram_image: 'https://via.placeholder.com/1080x1080/4F46E5/FFFFFF?text=Instagram+Post',\n    instagram_story: 'https://via.placeholder.com/1080x1920/4F46E5/FFFFFF?text=Instagram+Story',\n    linkedin_image: 'https://via.placeholder.com/1200x627/4F46E5/FFFFFF?text=LinkedIn+Post',\n    twitter_image: 'https://via.placeholder.com/1200x675/4F46E5/FFFFFF?text=Twitter+Post',\n    pinterest_image: 'https://via.placeholder.com/1000x1500/4F46E5/FFFFFF?text=Pinterest+Pin',\n    youtube_thumbnail: 'https://via.placeholder.com/1280x720/4F46E5/FFFFFF?text=YouTube+Video',\n    image_alt: `${contentData.primary_keyword} - GOD Digital Marketing`,\n    image_credit: 'GOD Digital Marketing Brand Asset',\n    image_source: 'placeholder',\n    image_quality_score: 7,\n    platform_ready: true,\n    fallback_used: true\n  };\n}"}, "id": "image-processor", "name": "Enhanced Image Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-800, 150]}, {"parameters": {"url": "https://graph.facebook.com/v18.0/me/feed", "authentication": "predefinedCredentialType", "nodeCredentialType": "facebookGraphApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "message", "value": "={{ $('Ultimate Content Processor').item.json.facebook_post }}"}, {"name": "link", "value": "https://godigitalmarketing.com"}, {"name": "published", "value": true}]}, "options": {}}, "id": "facebook-post", "name": "Facebook Page Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-600, -100]}, {"parameters": {"url": "https://graph.facebook.com/v18.0/me/media", "authentication": "predefinedCredentialType", "nodeCredentialType": "facebookGraphApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "image_url", "value": "={{ $('Enhanced Image Processor').item.json.instagram_image }}"}, {"name": "caption", "value": "={{ $('Ultimate Content Processor').item.json.instagram_caption }}"}, {"name": "media_type", "value": "IMAGE"}]}, "options": {}}, "id": "instagram-post", "name": "Instagram Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-600, -40]}, {"parameters": {"text": "={{ $('Ultimate Content Processor').item.json.twitter_thread }}", "additionalFields": {"attachments": "={{ $('Enhanced Image Processor').item.json.twitter_image }}"}}, "id": "twitter-post", "name": "Twitter/X Thread", "type": "n8n-nodes-base.twitter", "typeVersion": 2, "position": [-600, 20], "credentials": {"twitterOAuth2Api": {"id": "YOUR_TWITTER_API_CREDENTIAL_ID", "name": "Twitter API"}}}, {"parameters": {"text": "={{ $('Ultimate Content Processor').item.json.linkedin_post }}", "additionalFields": {"visibility": "public"}}, "id": "linkedin-post", "name": "LinkedIn Professional Post", "type": "n8n-nodes-base.linkedIn", "typeVersion": 1, "position": [-600, 80], "credentials": {"linkedInOAuth2Api": {"id": "YOUR_LINKEDIN_API_CREDENTIAL_ID", "name": "LinkedIn API"}}}, {"parameters": {"url": "https://api.pinterest.com/v5/pins", "authentication": "predefinedCredentialType", "nodeCredentialType": "pinterestOAuth2Api", "sendBody": true, "bodyParameters": {"parameters": [{"name": "board_id", "value": "YOUR_PINTEREST_BOARD_ID"}, {"name": "media_source", "value": {"source_type": "image_url", "url": "={{ $('Enhanced Image Processor').item.json.pinterest_image }}"}}, {"name": "title", "value": "={{ $('Ultimate Content Processor').item.json.pinterest_title }}"}, {"name": "description", "value": "={{ $('Ultimate Content Processor').item.json.pinterest_description }}"}, {"name": "link", "value": "https://godigitalmarketing.com"}]}, "options": {}}, "id": "pinterest-post", "name": "Pinterest SEO Pin", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-600, 140]}, {"parameters": {"url": "https://www.reddit.com/api/submit", "authentication": "predefinedCredentialType", "nodeCredentialType": "redditOAuth2Api", "sendBody": true, "bodyParameters": {"parameters": [{"name": "sr", "value": "entrepreneur"}, {"name": "kind", "value": "self"}, {"name": "title", "value": "={{ $('Ultimate Content Processor').item.json.reddit_title }}"}, {"name": "text", "value": "={{ $('Ultimate Content Processor').item.json.reddit_post }}"}]}, "options": {}}, "id": "reddit-post", "name": "Reddit Community Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-400, -100]}, {"parameters": {"url": "https://api.telegram.org/bot{{ $credentials.telegramApi.token }}/sendPhoto", "sendBody": true, "bodyParameters": {"parameters": [{"name": "chat_id", "value": "YOUR_TELEGRAM_CHANNEL_ID"}, {"name": "photo", "value": "={{ $('Enhanced Image Processor').item.json.image_url }}"}, {"name": "caption", "value": "={{ $('Ultimate Content Processor').item.json.telegram_message }}"}, {"name": "parse_mode", "value": "HTML"}]}, "options": {}}, "id": "telegram-post", "name": "Telegram Channel Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-400, -40]}, {"parameters": {"url": "https://discord.com/api/webhooks/YOUR_DISCORD_WEBHOOK_URL", "sendBody": true, "bodyParameters": {"parameters": [{"name": "content", "value": "={{ $('Ultimate Content Processor').item.json.discord_message }}"}, {"name": "embeds", "value": [{"title": "GOD Digital Marketing - {{ $('Strategic Content Calendar').item.json.content_focus }}", "description": "{{ $('Ultimate Content Processor').item.json.content_angle }}", "color": 5814783, "image": {"url": "{{ $('Enhanced Image Processor').item.json.image_url }}"}, "footer": {"text": "GOD Digital Marketing - Transforming Businesses"}}]}]}, "options": {}}, "id": "discord-post", "name": "Discord Community Update", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-400, 20]}, {"parameters": {"jsCode": "// Performance Analytics & Success Tracking\nconst results = [];\nconst errors = [];\nconst platforms = [\n  'Facebook Page Post',\n  'Instagram Post', \n  'Twitter/X Thread',\n  'LinkedIn Professional Post',\n  'Pinterest SEO Pin',\n  'Reddit Community Post',\n  'Telegram Channel Post',\n  'Discord Community Update'\n];\n\n// Collect results from all platform posting attempts\nplatforms.forEach(platform => {\n  try {\n    const result = $(platform).item;\n    if (result && result.json) {\n      results.push({\n        platform: platform,\n        status: 'success',\n        response: result.json,\n        timestamp: new Date().toISOString()\n      });\n    }\n  } catch (error) {\n    errors.push({\n      platform: platform,\n      status: 'failed',\n      error: error.message,\n      timestamp: new Date().toISOString()\n    });\n  }\n});\n\nconst successCount = results.length;\nconst failureCount = errors.length;\nconst totalPlatforms = platforms.length;\nconst successRate = (successCount / totalPlatforms) * 100;\n\nconst contentData = $('Ultimate Content Processor').item.json;\nconst strategyData = $('Strategic Content Calendar').item.json;\n\nreturn {\n  posting_results: results,\n  posting_errors: errors,\n  success_count: successCount,\n  failure_count: failureCount,\n  success_rate: successRate,\n  total_platforms: totalPlatforms,\n  content_type: strategyData.content_type,\n  content_quality_score: contentData.quality_metrics.overall_score,\n  day_name: strategyData.day_name,\n  rotation_day: strategyData.rotation_day,\n  campaign_summary: {\n    hook: contentData.hook,\n    lead_magnet: contentData.lead_magnet,\n    primary_keyword: contentData.primary_keyword\n  },\n  needs_retry: failureCount > 0,\n  timestamp: new Date().toISOString()\n};"}, "id": "analytics-tracker", "name": "Performance Analytics", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-200, 50]}, {"parameters": {"text": "🎯 *GOD Digital Marketing - Daily Campaign Report*\\n\\n*📅 {{ $('Performance Analytics').item.json.day_name }} - {{ $('Performance Analytics').item.json.content_type.toUpperCase() }} Content*\\n\\n*📊 Campaign Performance:*\\n• Success Rate: {{ $('Performance Analytics').item.json.success_rate }}%\\n• Platforms Posted: {{ $('Performance Analytics').item.json.success_count }}/{{ $('Performance Analytics').item.json.total_platforms }}\\n• Content Quality: {{ $('Performance Analytics').item.json.content_quality_score }}/10\\n\\n*🎯 Today's Strategy:*\\n• Hook: {{ $('Performance Analytics').item.json.campaign_summary.hook }}\\n• Lead Magnet: {{ $('Performance Analytics').item.json.campaign_summary.lead_magnet }}\\n• Primary Keyword: {{ $('Performance Analytics').item.json.campaign_summary.primary_keyword }}\\n\\n*📱 Platforms Deployed:*\\n• ✅ Facebook (Page + Community)\\n• ✅ Instagram (Feed + Stories)\\n• ✅ Twitter/X (Threads)\\n• ✅ LinkedIn (Professional)\\n• ✅ Pinterest (SEO-Optimized)\\n• ✅ Reddit (Community)\\n• ✅ Telegram (Channel)\\n• ✅ Discord (Community)\\n\\n*💰 ROI Impact:*\\n• Cost: $0 (100% Automated)\\n• Time Saved: 4+ hours\\n• Reach Potential: 50,000+ people\\n• Lead Generation: High-converting content\\n\\n*🚀 Next Steps:*\\n• Monitor engagement in next 24 hours\\n• Track lead generation metrics\\n• Prepare tomorrow's {{ ['Educational', 'Achievement', 'Psychology', 'Trends', 'Resources', 'Community', 'Motivational'][($('Performance Analytics').item.json.rotation_day % 7)] }} content\\n\\n*Campaign ID:* {{ $workflow.id }}_{{ new Date().toISOString().split('T')[0] }}", "otherOptions": {}}, "id": "success-notification", "name": "Comprehensive Success Report", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [0, 50], "webhookId": "YOUR_SLACK_WEBHOOK_ID"}], "connections": {"Daily Content Scheduler": {"main": [[{"node": "Strategic Configuration", "type": "main", "index": 0}]]}, "Manual Test Trigger": {"main": [[{"node": "Strategic Configuration", "type": "main", "index": 0}]]}, "Strategic Configuration": {"main": [[{"node": "Strategic Content Calendar", "type": "main", "index": 0}]]}, "Strategic Content Calendar": {"main": [[{"node": "Industry Trends Research", "type": "main", "index": 0}]]}, "Industry Trends Research": {"main": [[{"node": "Advanced Trend Analyzer", "type": "main", "index": 0}]]}, "Advanced Trend Analyzer": {"main": [[{"node": "Master Content Creator AI", "type": "main", "index": 0}]]}, "Master Content Creator AI": {"main": [[{"node": "Ultimate Content Processor", "type": "main", "index": 0}]]}, "Ultimate Content Processor": {"main": [[{"node": "Professional Image Search", "type": "main", "index": 0}]]}, "Professional Image Search": {"main": [[{"node": "Enhanced Image Processor", "type": "main", "index": 0}]]}, "Enhanced Image Processor": {"main": [[{"node": "Facebook Page Post", "type": "main", "index": 0}, {"node": "Instagram Post", "type": "main", "index": 0}, {"node": "Twitter/X Thread", "type": "main", "index": 0}, {"node": "LinkedIn Professional Post", "type": "main", "index": 0}, {"node": "Pinterest SEO Pin", "type": "main", "index": 0}, {"node": "Reddit Community Post", "type": "main", "index": 0}, {"node": "Telegram Channel Post", "type": "main", "index": 0}, {"node": "Discord Community Update", "type": "main", "index": 0}]]}, "Facebook Page Post": {"main": [[{"node": "Performance Analytics", "type": "main", "index": 0}]]}, "Instagram Post": {"main": [[{"node": "Performance Analytics", "type": "main", "index": 0}]]}, "Twitter/X Thread": {"main": [[{"node": "Performance Analytics", "type": "main", "index": 0}]]}, "LinkedIn Professional Post": {"main": [[{"node": "Performance Analytics", "type": "main", "index": 0}]]}, "Pinterest SEO Pin": {"main": [[{"node": "Performance Analytics", "type": "main", "index": 0}]]}, "Reddit Community Post": {"main": [[{"node": "Performance Analytics", "type": "main", "index": 0}]]}, "Telegram Channel Post": {"main": [[{"node": "Performance Analytics", "type": "main", "index": 0}]]}, "Discord Community Update": {"main": [[{"node": "Performance Analytics", "type": "main", "index": 0}]]}, "Performance Analytics": {"main": [[{"node": "Comprehensive Success Report", "type": "main", "index": 0}]]}, "Advanced Groq AI Model": {"ai_languageModel": [[{"node": "Master Content Creator AI", "type": "ai_languageModel", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "god-digital-marketing", "name": "GOD Digital Marketing"}], "triggerCount": 2, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1", "meta": {"templateCredsSetupCompleted": false, "instanceId": "god-digital-marketing-ultimate-workflow"}}